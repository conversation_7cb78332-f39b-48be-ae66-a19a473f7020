<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestione AGEMAR - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>


        /* === TEMA MARITTIMO PROFESSIONALE OTTIMIZZATO === */
        body {
            background: linear-gradient(135deg, #1a252f 0%, #2c3e50 50%, #34495e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #2c3e50;
        }

        /* === OTTIMIZZAZIONI TEMA SCURO === */
        [data-theme="dark"] .filter-section {
            background: rgba(45, 55, 72, 0.95) !important;
            border: 2px solid rgba(226, 232, 240, 0.2) !important;
        }

        [data-theme="dark"] .form-label {
            color: #f7fafc !important;
        }

        [data-theme="dark"] .form-select {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
            border-color: rgba(226, 232, 240, 0.3) !important;
        }

        [data-theme="dark"] .stats-row {
            background: rgba(26, 32, 44, 0.95) !important;
            border: 2px solid rgba(226, 232, 240, 0.2) !important;
        }

        [data-theme="dark"] .stat-label {
            color: #e2e8f0 !important;
        }

        [data-theme="dark"] .stat-number {
            color: #60a5fa !important;
        }

        [data-theme="dark"] .empty-message {
            background: rgba(45, 55, 72, 0.95) !important;
            border: 2px solid rgba(226, 232, 240, 0.2) !important;
            border-radius: 20px;
            margin: 2rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        [data-theme="dark"] .empty-message h4 {
            color: #3a759c !important;
        }

        [data-theme="dark"] .empty-message p {
            color: #e2e8f0 !important;
        }

        [data-theme="dark"] .empty-message i {
            color: #60a5fa !important;
        }

        [data-theme="dark"] .agemar-table {
            background: rgba(26, 32, 44, 0.95) !important;
            border: 2px solid rgba(226, 232, 240, 0.2) !important;
        }

        [data-theme="dark"] .table td {
            border-color: rgba(226, 232, 240, 0.1) !important;
        }

        [data-theme="dark"] .table tbody tr:hover {
            background: rgba(45, 55, 72, 0.7) !important;
        }

        /* === CLASSI TESTO TABELLA === */
        .table-text-primary {
            color: #2c3e50;
            font-weight: 600;
        }

        .table-text-secondary {
            color: #5d6d7e;
            font-weight: 500;
        }

        .table-text-muted {
            color: #7f8c8d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* === CLASSI TESTO TABELLA TEMA SCURO === */
        [data-theme="dark"] .table-text-primary {
            color: #f7fafc !important;
        }

        [data-theme="dark"] .table-text-secondary {
            color: #e2e8f0 !important;
        }

        [data-theme="dark"] .table-text-muted {
            color: #cbd5e0 !important;
        }

        .main-content {
            margin-top: 20px;
        }

        /* === CARD PRINCIPALE OTTIMIZZATA === */
        .content-card {
            background: rgba(255, 255, 255, 0.99);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(52, 152, 219, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db 0%, #2980b9 50%, #1abc9c 100%);
        }

        /* === HEADER SEZIONE === */
        .page-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 1.5rem;
            margin: -1.5rem -1.5rem 2rem -1.5rem;
            border-radius: 25px 25px 0 0;
            position: relative;
        }

        .page-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #1abc9c);
            border-radius: 2px;
        }

        .page-header h2 {
            margin: 0;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }



        /* === SEZIONE FILTRI === */
        .filter-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid rgba(52, 152, 219, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .form-label {
            color: #2c3e50;
            font-size: 1rem;
            margin-bottom: 0.75rem;
        }

        .form-select {
            border: 2px solid #bdc3c7;
            border-radius: 15px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            color: #2c3e50;
            background: white;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            outline: none;
        }

        .form-select:hover {
            border-color: #3498db;
        }

        /* === STATISTICHE === */
        .stats-row {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .stat-item {
            text-align: center;
            position: relative;
            padding: 1rem;
        }

        .stat-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #1abc9c);
            border-radius: 2px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1e3a8a;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            /* Fallback per browser che non supportano gradient text */
            background: linear-gradient(135deg, #1e3a8a 0%, #3498db 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #5d6d7e;
            font-size: 0.95rem;
            font-weight: 600;
            margin-top: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* === TABELLA === */
        .agemar-table {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(52, 152, 219, 0.1);
        }

        .table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            border: none;
            font-weight: 700;
            text-align: center;
            padding: 1.2rem 0.8rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #1abc9c);
        }

        .table td {
            vertical-align: middle;
            text-align: center;
            border-color: #ecf0f1;
            padding: 1rem 0.8rem;
            font-weight: 500;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        /* === BADGE OTTIMIZZATI === */
        .badge-agemar {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow: 0 3px 12px rgba(5, 150, 105, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .badge-percentage {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow: 0 3px 12px rgba(220, 38, 38, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }



        /* === LOADING SPINNER === */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 3rem;
            color: #5d6d7e;
        }

        .loading-spinner .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.3rem;
            border-color: #3498db;
            border-right-color: transparent;
        }

        /* === MESSAGGIO VUOTO === */
        .empty-message {
            text-align: center;
            padding: 4rem 2rem;
            color: #7f8c8d;
        }

        .empty-message i {
            color: #bdc3c7;
            margin-bottom: 1.5rem;
        }

        .empty-message h4 {
            color: #5d6d7e;
            font-weight: 600;
        }

        /* === ANIMAZIONI === */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .stats-row {
            animation: fadeInUp 0.8s ease-out;
        }

        .agemar-table {
            animation: fadeInUp 1s ease-out;
        }

        /* === RESPONSIVE === */
        @media (max-width: 768px) {
            .stat-number {
                font-size: 2rem;
            }

            .page-header {
                padding: 1rem;
                margin: -1rem -1rem 1.5rem -1rem;
            }

            .stats-row {
                padding: 1.5rem;
            }

            .table th, .table td {
                padding: 0.8rem 0.5rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    {% include 'components/navbar.html' %}
    
    <div class="container main-content">
        <div class="content-card p-4">
            <!-- Header della pagina -->
            <div class="page-header">
                <h2><i class="fas fa-ship me-3"></i>Calcoli AGEMAR per Trimestre</h2>
            </div>

            <!-- Selezione Trimestre -->
            <div class="filter-section mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="year-select" class="form-label fw-bold">
                            <i class="fas fa-calendar-alt me-2"></i>Seleziona Anno:
                        </label>
                        <select class="form-select" id="year-select" onchange="loadTrimesters()">
                            <option value="">Caricamento anni...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="trimester-select" class="form-label fw-bold">
                            <i class="fas fa-calendar-week me-2"></i>Seleziona Trimestre:
                        </label>
                        <select class="form-select" id="trimester-select" onchange="loadAgemar_Data()" disabled>
                            <option value="">Prima seleziona un anno...</option>
                            <option value="1">Q1 - Gennaio, Febbraio, Marzo</option>
                            <option value="2">Q2 - Aprile, Maggio, Giugno</option>
                            <option value="3">Q3 - Luglio, Agosto, Settembre</option>
                            <option value="4">Q4 - Ottobre, Novembre, Dicembre</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Statistiche Trimestre -->
            <div class="stats-row" id="stats-section" style="display: none;">
                <div class="row justify-content-center">
                    <div class="col-md-4">
                        <div class="stat-item">
                            <div class="stat-number" id="total-records">-</div>
                            <div class="stat-label">Viaggi Trimestre</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <div class="stat-number" id="total-agemar">-</div>
                            <div class="stat-label">Totale AGEMAR</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <div class="stat-number" id="total-percentage">-</div>
                            <div class="stat-label">Totale 6,80%</div>
                        </div>
                    </div>
                </div>

                <!-- Pulsante Export Excel -->
                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <button class="btn btn-success btn-lg" id="export-excel-btn" onclick="exportToExcel()" style="display: none;">
                            <i class="fas fa-file-excel me-2"></i>Esporta Report Excel
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Spinner di caricamento -->
            <div class="loading-spinner" id="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
                <p class="mt-2">Caricamento calcoli AGEMAR...</p>
            </div>
            
            <!-- Tabella AGEMAR -->
            <div class="agemar-table" id="agemar-table" style="display: none;">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Nome Nave</th>
                            <th>Porto Gestione</th>
                            <th>TSL</th>
                            <th>AGEMAR</th>
                            <th>Percentuale 6,80%</th>
                            <th>Data Arrivo</th>
                        </tr>
                    </thead>
                    <tbody id="agemar-tbody">
                        <!-- I dati verranno caricati dinamicamente -->
                    </tbody>
                </table>
            </div>
            
            <!-- Messaggio selezione trimestre -->
            <div class="empty-message" id="select-message">
                <i class="fas fa-calendar-alt fa-4x"></i>
                <h4>Seleziona Anno e Trimestre</h4>
                <p>Gli anni disponibili sono basati sulle date di arrivo delle navi nel database.<br>
                Scegli l'anno e il trimestre per visualizzare i calcoli AGEMAR del periodo selezionato.</p>
            </div>

            <!-- Messaggio vuoto -->
            <div class="empty-message" id="empty-message" style="display: none;">
                <i class="fas fa-ship fa-4x"></i>
                <h4>Nessun calcolo AGEMAR trovato</h4>
                <p>Non ci sono calcoli AGEMAR per il trimestre selezionato.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let allAgemar_Data = []; // Cache di tutti i dati

        document.addEventListener('DOMContentLoaded', async function() {
            // Prima carica i dati, poi gli anni basandosi sui dati
            await loadAllAgemar_Data();
            loadYears();

            // Rileva e applica tema scuro se necessario
            detectTheme();
        });

        // Funzione semplice per rilevare il tema
        function detectTheme() {
            // Controlla se il body ha classi che indicano tema scuro
            const bodyClasses = document.body.className;
            const htmlClasses = document.documentElement.className;

            if (bodyClasses.includes('dark') || bodyClasses.includes('theme-dark') ||
                htmlClasses.includes('dark') || htmlClasses.includes('theme-dark')) {
                document.documentElement.setAttribute('data-theme', 'dark');
                console.log('🌙 Tema scuro rilevato e applicato');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                console.log('☀️ Tema chiaro applicato');
            }
        }

        // Carica gli anni disponibili basandosi sui dati reali
        function loadYears() {
            const yearSelect = document.getElementById('year-select');

            // Aspetta che i dati siano caricati
            if (allAgemar_Data.length === 0) {
                // Se i dati non sono ancora caricati, riprova dopo un breve delay
                setTimeout(loadYears, 500);
                return;
            }

            // Estrai tutti gli anni dalle date di arrivo
            const years = new Set();
            allAgemar_Data.forEach(record => {
                if (record.data_arrivo) {
                    const year = new Date(record.data_arrivo).getFullYear();
                    if (!isNaN(year)) {
                        years.add(year);
                    }
                }
            });

            // Converti in array e ordina
            const sortedYears = Array.from(years).sort((a, b) => b - a); // Ordine decrescente

            // Pulisci il select
            yearSelect.innerHTML = '';

            if (sortedYears.length === 0) {
                // Nessun anno trovato
                yearSelect.innerHTML = '<option value="">Nessun dato disponibile</option>';
                yearSelect.disabled = true;
                console.log('⚠️ Nessun anno trovato nei dati AGEMAR');
                return;
            }

            // Aggiungi opzione di selezione
            yearSelect.innerHTML = '<option value="">Seleziona un anno...</option>';
            yearSelect.disabled = false;

            // Aggiungi gli anni trovati
            const currentYear = new Date().getFullYear();
            let hasCurrentYear = false;

            sortedYears.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;

                // Seleziona l'anno corrente se presente, altrimenti il più recente
                if (year === currentYear) {
                    option.selected = true;
                    hasCurrentYear = true;
                }

                yearSelect.appendChild(option);
            });

            // Se l'anno corrente non è presente, seleziona il più recente
            if (!hasCurrentYear && sortedYears.length > 0) {
                yearSelect.children[1].selected = true; // Primo anno dopo "Seleziona..."
            }

            console.log(`📅 Caricati ${sortedYears.length} anni dal database:`, sortedYears);

            // Abilita il trimestre se un anno è stato selezionato automaticamente
            setTimeout(() => {
                if (yearSelect.value && yearSelect.value !== "") {
                    console.log(`🔧 Chiamata loadTrimesters() per anno auto-selezionato: ${yearSelect.value}`);
                    loadTrimesters();
                } else {
                    console.log(`⚠️ Nessun anno auto-selezionato, trimestre rimane disabilitato`);
                }
            }, 100); // Piccolo delay per assicurarsi che la selezione sia completata
        }

        // Carica tutti i dati AGEMAR una volta
        async function loadAllAgemar_Data() {
            try {
                const response = await fetch('/api/calcolo-agemar');
                const data = await response.json();

                if (data.success) {
                    allAgemar_Data = data.data || [];
                    console.log(`Caricati ${allAgemar_Data.length} record AGEMAR`);
                } else {
                    console.error('Errore nel caricamento dati AGEMAR:', data.error);
                    allAgemar_Data = [];
                }
            } catch (error) {
                console.error('Errore nel caricamento dati AGEMAR:', error);
                allAgemar_Data = [];
            }
        }

        // Filtra e visualizza i dati per il trimestre selezionato
        function loadAgemar_Data() {
            const yearSelect = document.getElementById('year-select');
            const trimesterSelect = document.getElementById('trimester-select');
            const loading = document.getElementById('loading');
            const table = document.getElementById('agemar-table');
            const emptyMessage = document.getElementById('empty-message');
            const selectMessage = document.getElementById('select-message');
            const statsSection = document.getElementById('stats-section');

            const selectedYear = yearSelect.value;
            const selectedTrimester = trimesterSelect.value;

            // Nascondi tutto inizialmente
            loading.style.display = 'none';
            table.style.display = 'none';
            emptyMessage.style.display = 'none';
            selectMessage.style.display = 'none';
            statsSection.style.display = 'none';

            // Se non è selezionato un trimestre, mostra messaggio di selezione
            if (!selectedTrimester || !selectedYear) {
                selectMessage.style.display = 'block';
                return;
            }

            // Filtra i dati per anno e trimestre
            const filteredData = filterDataByTrimester(allAgemar_Data, selectedYear, selectedTrimester);

            if (filteredData.length > 0) {
                populateTable(filteredData);
                updateStats(filteredData);
                table.style.display = 'block';
                statsSection.style.display = 'block';
                document.getElementById('export-excel-btn').style.display = 'inline-block';
            } else {
                emptyMessage.style.display = 'block';
                document.getElementById('export-excel-btn').style.display = 'none';
            }
        }

        // Filtra i dati per anno e trimestre basandosi sulla data_arrivo
        function filterDataByTrimester(data, year, trimester) {
            return data.filter(record => {
                // Usa data_arrivo invece di created_at
                if (!record.data_arrivo) return false;

                const recordDate = new Date(record.data_arrivo);
                const recordYear = recordDate.getFullYear();
                const recordMonth = recordDate.getMonth() + 1; // getMonth() è 0-based

                // Verifica anno
                if (recordYear !== parseInt(year)) return false;

                // Verifica trimestre
                const trimesterMonths = {
                    '1': [1, 2, 3],     // Q1: Gen, Feb, Mar
                    '2': [4, 5, 6],     // Q2: Apr, Mag, Giu
                    '3': [7, 8, 9],     // Q3: Lug, Ago, Set
                    '4': [10, 11, 12]   // Q4: Ott, Nov, Dic
                };

                return trimesterMonths[trimester].includes(recordMonth);
            });
        }

        // Aggiorna i trimestri quando cambia l'anno
        function loadTrimesters() {
            const yearSelect = document.getElementById('year-select');
            const trimesterSelect = document.getElementById('trimester-select');

            // Reset selezione trimestre
            trimesterSelect.value = '';

            // Abilita/disabilita il select trimestre basandosi sulla selezione anno
            if (yearSelect.value && yearSelect.value !== "") {
                trimesterSelect.disabled = false;
                trimesterSelect.children[0].textContent = "Seleziona un trimestre...";
            } else {
                trimesterSelect.disabled = true;
                trimesterSelect.children[0].textContent = "Prima seleziona un anno...";
            }

            // Mostra messaggio di selezione
            const selectMessage = document.getElementById('select-message');
            const table = document.getElementById('agemar-table');
            const emptyMessage = document.getElementById('empty-message');
            const statsSection = document.getElementById('stats-section');

            table.style.display = 'none';
            emptyMessage.style.display = 'none';
            statsSection.style.display = 'none';
            selectMessage.style.display = 'block';
        }

        function populateTable(data) {
            const tbody = document.getElementById('agemar-tbody');
            tbody.innerHTML = '';

            data.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong class="table-text-primary">${record.nome_nave}</strong></td>
                    <td class="table-text-secondary">${record.porto_gestione || 'N/A'}</td>
                    <td><strong class="table-text-primary">${record.TSL ? parseFloat(record.TSL).toLocaleString('it-IT') : '-'}</strong></td>
                    <td><span class="badge-agemar">€ ${record.AGEMAR ? parseFloat(record.AGEMAR).toFixed(2) : '0.00'}</span></td>
                    <td><span class="badge-percentage">€ ${record.percentuale_agemar ? parseFloat(record.percentuale_agemar).toFixed(2) : '0.00'}</span></td>
                    <td class="table-text-muted">${record.data_arrivo ? new Date(record.data_arrivo).toLocaleDateString('it-IT') : '-'}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateStats(data) {
            const totalRecords = data.length;
            const totalAgemar = data.reduce((sum, record) => sum + (parseFloat(record.AGEMAR) || 0), 0);
            const totalPercentage = data.reduce((sum, record) => sum + (parseFloat(record.percentuale_agemar) || 0), 0);

            // Aggiorna le statistiche con animazione
            animateNumber('total-records', totalRecords);
            animateNumber('total-agemar', totalAgemar, '€ ', 2);
            animateNumber('total-percentage', totalPercentage, '€ ', 2);
        }

        // Anima i numeri delle statistiche
        function animateNumber(elementId, targetValue, prefix = '', decimals = 0) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 1000; // 1 secondo
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function per animazione fluida
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = startValue + (targetValue - startValue) * easeOutQuart;

                if (decimals > 0) {
                    element.textContent = prefix + currentValue.toFixed(decimals);
                } else {
                    element.textContent = prefix + Math.round(currentValue);
                }

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }

            requestAnimationFrame(updateNumber);
        }

        // Funzione per esportare i dati in Excel
        function exportToExcel() {
            const yearSelect = document.getElementById('year-select');
            const trimesterSelect = document.getElementById('trimester-select');
            const selectedYear = yearSelect.value;
            const selectedTrimester = trimesterSelect.value;

            if (!selectedYear || !selectedTrimester) {
                alert('Seleziona anno e trimestre prima di esportare');
                return;
            }

            // Filtra i dati per il trimestre selezionato
            const filteredData = filterDataByTrimester(allAgemar_Data, selectedYear, selectedTrimester);

            if (filteredData.length === 0) {
                alert('Nessun dato da esportare per il trimestre selezionato');
                return;
            }

            // Crea il form per inviare i dati
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/contabilita/agemar/export-excel';
            form.style.display = 'none';

            // Aggiungi i dati come campo nascosto
            const dataInput = document.createElement('input');
            dataInput.type = 'hidden';
            dataInput.name = 'data';
            dataInput.value = JSON.stringify(filteredData);
            form.appendChild(dataInput);

            // Aggiungi informazioni sul trimestre
            const yearInput = document.createElement('input');
            yearInput.type = 'hidden';
            yearInput.name = 'year';
            yearInput.value = selectedYear;
            form.appendChild(yearInput);

            const trimesterInput = document.createElement('input');
            trimesterInput.type = 'hidden';
            trimesterInput.name = 'trimester';
            trimesterInput.value = selectedTrimester;
            form.appendChild(trimesterInput);

            // Invia il form
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

    </script>
</body>
</html>
