# 🔐 Sistema di Autenticazione SNIP

Sistema di sicurezza per l'avvio automatico del servizio FastAPI con autenticazione via email.

## 📋 Panoramica

Questo sistema implementa un meccanismo di sicurezza che:

1. **All'avvio di Windows** si attiva automaticamente
2. **Invia un codice di 6 cifre** a `<EMAIL>`
3. **Richiede l'inserimento del codice** tramite interfaccia web
4. **Avvia il servizio FastAPI** solo dopo l'autenticazione riuscita

## 🚀 Installazione Rapida

### 1. Prerequisiti
- Windows 10/11
- Python 3.8 o superiore
- Connessione internet

### 2. Installazione Automatica
1. **Esegui come Amministratore**: `install_startup.bat`
2. Segui le istruzioni a schermo
3. Il sistema sarà configurato per l'avvio automatico

### 3. Test Manuale (Opzionale)
```bash
# Test del sistema senza installazione
start_snip_auth.bat
```

## 📁 File del Sistema

### File Principali
- `auth_system.py` - Generatore e verificatore di codici di sicurezza
- `auth_server.py` - Server Flask per l'interfaccia di autenticazione
- `templates/auth_login.html` - Interfaccia web di login

### Script di Avvio
- `start_snip_auth.bat` - Avvio manuale (Windows Batch)
- `start_snip_auth.ps1` - Avvio avanzato (PowerShell)
- `install_startup.bat` - Installazione avvio automatico
- `uninstall_startup.bat` - Rimozione avvio automatico

### Configurazione
- `auth_config.json` - Configurazione del sistema (generato automaticamente)
- `auth_config_example.json` - Esempio di configurazione

## ⚙️ Configurazione Email

Il sistema utilizza Gmail SMTP con le seguenti impostazioni:

```json
{
    "email": {
        "smtp_host": "smtp.gmail.com",
        "smtp_port": 587,
        "smtp_username": "<EMAIL>",
        "smtp_password": "sxfv xkjf afur vuhr",
        "sender_email": "<EMAIL>",
        "target_email": "<EMAIL>",
        "smtp_ssl": true
    }
}
```

### ⚠️ Importante: Password Gmail
La password `sxfv xkjf afur vuhr` deve essere una **Password per le App** di Gmail:

1. Vai su [myaccount.google.com](https://myaccount.google.com)
2. Sicurezza → Verifica in due passaggi (deve essere attiva)
3. Sicurezza → Password per le app
4. Genera una nuova password per "SNIP Email System"
5. Usa quella password di 16 caratteri nel sistema

## 🔧 Funzionamento

### Sequenza di Avvio
1. **Windows si avvia** → Task schedulato attiva il sistema
2. **Sistema invia email** → Codice di 6 <NAME_EMAIL>
3. **Browser si apre** → http://localhost:5000 (interfaccia di autenticazione)
4. **Utente inserisce codice** → Verifica tramite interfaccia web
5. **Se codice corretto** → FastAPI si avvia su http://localhost:8000
6. **Redirect automatico** → L'utente viene reindirizzato al sistema SNIP

### Sicurezza
- **Codice di 6 cifre** generato casualmente
- **Validità limitata**: 10 minuti
- **Tentativi limitati**: 3 tentativi per codice
- **Codice monouso**: Invalido dopo l'uso corretto

## 🌐 Interfacce Web

### Autenticazione: http://localhost:5000
- Interfaccia moderna e responsive
- Inserimento codice di sicurezza
- Richiesta nuovo codice
- Informazioni sui tentativi rimanenti
- Countdown scadenza codice

### Sistema SNIP: http://localhost:8000
- Disponibile solo dopo l'autenticazione
- Sistema FastAPI completo
- Tutte le funzionalità originali

## 📊 Monitoraggio e Log

### File di Log
- `auth_system.log` - Log del sistema di autenticazione
- `auth_server.log` - Log del server Flask
- `logs/startup.log` - Log di avvio (PowerShell)

### Monitoraggio
```bash
# Visualizza log in tempo reale
tail -f auth_system.log
tail -f auth_server.log
```

## 🛠️ Gestione del Sistema

### Comandi Utili

#### Installazione/Disinstallazione
```bash
# Installa avvio automatico (come Admin)
install_startup.bat

# Rimuovi avvio automatico (come Admin)
uninstall_startup.bat
```

#### Avvio Manuale
```bash
# Avvio semplice
start_snip_auth.bat

# Avvio avanzato con browser automatico
powershell -ExecutionPolicy Bypass -File start_snip_auth.ps1 -AutoOpen
```

#### Test Sistema
```bash
# Test generazione e invio codice
python auth_system.py

# Test server di autenticazione
python auth_server.py
```

### Task Schedulato di Windows
- **Nome**: `SNIP_Authentication_System`
- **Trigger**: All'avvio del sistema
- **Azione**: Esegue `start_snip_auth.ps1`
- **Utente**: Utente corrente
- **Privilegi**: Interattivo

## 🔍 Risoluzione Problemi

### Problema: Email non arriva
**Cause possibili:**
- Password Gmail non corretta
- Autenticazione a 2 fattori non attiva
- Connessione internet assente

**Soluzioni:**
1. Verifica la password per le app Gmail
2. Controlla i log in `auth_system.log`
3. Testa manualmente: `python auth_system.py`

### Problema: Sistema non si avvia automaticamente
**Cause possibili:**
- Task schedulato non installato
- Privilegi insufficienti
- Policy di esecuzione PowerShell

**Soluzioni:**
1. Reinstalla con `install_startup.bat` (come Admin)
2. Verifica in "Utilità di pianificazione"
3. Configura policy PowerShell: `Set-ExecutionPolicy RemoteSigned`

### Problema: FastAPI non si avvia
**Cause possibili:**
- Porta 8000 occupata
- Dipendenze mancanti
- Errori nel codice principale

**Soluzioni:**
1. Verifica porte: `netstat -an | findstr :8000`
2. Installa dipendenze: `pip install -r requirements.txt`
3. Controlla log in `auth_server.log`

## 📞 Supporto

Per problemi o domande:
- Controlla i file di log
- Verifica la configurazione email
- Testa i componenti singolarmente

## 🔄 Aggiornamenti

Per aggiornare il sistema:
1. Sostituisci i file Python
2. Riavvia il sistema manualmente
3. Non è necessario reinstallare il task schedulato

---

**Michele Autuori Srl**  
Sistema Navale Integrato Portuale  
© 2025 - Sistema di Autenticazione SNIP
