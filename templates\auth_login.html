<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Autenticazione Sistema SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 450px;
            width: 100%;
            padding: 40px;
            text-align: center;
        }
        
        .auth-logo {
            font-size: 4rem;
            color: #2a5298;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .auth-title {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }
        
        .auth-subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1rem;
        }
        
        .code-input {
            font-size: 2rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: bold;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .code-input:focus {
            border-color: #2a5298;
            box-shadow: 0 0 20px rgba(42, 82, 152, 0.3);
            outline: none;
        }
        
        .btn-auth {
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(42, 82, 152, 0.3);
            color: white;
        }
        
        .btn-secondary-auth {
            background: transparent;
            border: 2px solid #6c757d;
            color: #6c757d;
            border-radius: 15px;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-secondary-auth:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-1px);
        }
        
        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .countdown {
            font-size: 0.9rem;
            color: #e74c3c;
            font-weight: 600;
            margin-top: 10px;
        }
        
        .attempts-info {
            font-size: 0.85rem;
            color: #7f8c8d;
            margin-top: 10px;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
        
        .company-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #7f8c8d;
            font-size: 0.85rem;
        }
        
        .security-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            
            <h1 class="auth-title">Sistema SNIP</h1>
            <p class="auth-subtitle">Inserisci il codice di sicurezza ricevuto via email</p>
            
            <!-- Messaggio di stato -->
            <div id="statusMessage" class="status-message" style="display: none;"></div>
            
            <!-- Form di autenticazione -->
            <form id="authForm">
                <div class="mb-3">
                    <input 
                        type="text" 
                        id="securityCode" 
                        class="form-control code-input" 
                        placeholder="000000"
                        maxlength="6"
                        pattern="[0-9]{6}"
                        required
                        autocomplete="off"
                    >
                </div>
                
                <button type="submit" class="btn btn-auth" id="verifyBtn">
                    <span class="loading" id="verifyLoading">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                    <span id="verifyText">
                        <i class="fas fa-key"></i> Verifica Codice
                    </span>
                </button>
                
                <button type="button" class="btn btn-secondary-auth" id="requestNewBtn">
                    <span class="loading" id="requestLoading">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                    <span id="requestText">
                        <i class="fas fa-envelope"></i> Richiedi Nuovo Codice
                    </span>
                </button>
            </form>
            
            <!-- Informazioni sui tentativi -->
            <div id="attemptsInfo" class="attempts-info" style="display: none;"></div>
            
            <!-- Countdown scadenza -->
            <div id="countdown" class="countdown" style="display: none;"></div>
            
            <!-- Informazioni di sicurezza -->
            <div class="security-info">
                <i class="fas fa-info-circle"></i>
                <strong>Informazioni di Sicurezza:</strong><br>
                • Il codice è valido per 10 minuti<br>
                • Hai a disposizione 3 tentativi<br>
                • Il codice viene <NAME_EMAIL>
            </div>
            
            <!-- Informazioni azienda -->
            <div class="company-info">
                <strong>Michele Autuori Srl</strong><br>
                shipping and forwarding agency<br>
                Sistema Navale Integrato Portuale
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let countdownInterval;
        
        // Elementi DOM
        const authForm = document.getElementById('authForm');
        const securityCodeInput = document.getElementById('securityCode');
        const verifyBtn = document.getElementById('verifyBtn');
        const requestNewBtn = document.getElementById('requestNewBtn');
        const statusMessage = document.getElementById('statusMessage');
        const attemptsInfo = document.getElementById('attemptsInfo');
        const countdownDiv = document.getElementById('countdown');
        
        // Carica stato iniziale
        document.addEventListener('DOMContentLoaded', function() {
            loadStatus();
            securityCodeInput.focus();
        });
        
        // Gestione form di verifica
        authForm.addEventListener('submit', function(e) {
            e.preventDefault();
            verifyCode();
        });
        
        // Richiesta nuovo codice
        requestNewBtn.addEventListener('click', function() {
            requestNewCode();
        });
        
        // Auto-formattazione input codice
        securityCodeInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 6) value = value.slice(0, 6);
            e.target.value = value;
        });
        
        // Funzioni API
        async function verifyCode() {
            const code = securityCodeInput.value.trim();
            
            if (code.length !== 6) {
                showMessage('Il codice deve essere di 6 cifre', 'error');
                return;
            }
            
            setLoading(verifyBtn, 'verifyLoading', 'verifyText', true);
            
            try {
                const response = await fetch('/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ code: code })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(result.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    showMessage(result.message, 'error');
                    updateAttemptsInfo(result.attempts_remaining);
                    
                    if (result.code_expired || result.attempts_remaining === 0) {
                        securityCodeInput.disabled = true;
                        verifyBtn.disabled = true;
                    }
                }
                
                securityCodeInput.value = '';
                
            } catch (error) {
                showMessage('Errore di connessione. Riprova.', 'error');
            } finally {
                setLoading(verifyBtn, 'verifyLoading', 'verifyText', false);
            }
        }
        
        async function requestNewCode() {
            setLoading(requestNewBtn, 'requestLoading', 'requestText', true);
            
            try {
                const response = await fetch('/auth/request-code', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('Nuovo codice inviato via email!', 'success');
                    securityCodeInput.disabled = false;
                    verifyBtn.disabled = false;
                    securityCodeInput.focus();
                    loadStatus();
                } else {
                    showMessage(result.message || 'Errore nell\'invio del codice', 'error');
                }
                
            } catch (error) {
                showMessage('Errore di connessione. Riprova.', 'error');
            } finally {
                setLoading(requestNewBtn, 'requestLoading', 'requestText', false);
            }
        }
        
        async function loadStatus() {
            try {
                const response = await fetch('/auth/status');
                const status = await response.json();
                
                if (status.has_active_code && !status.code_expired) {
                    updateAttemptsInfo(status.attempts_remaining);
                    if (status.expiry_time) {
                        startCountdown(status.expiry_time);
                    }
                }
                
            } catch (error) {
                console.error('Errore caricamento stato:', error);
            }
        }
        
        // Funzioni di utilità
        function showMessage(message, type) {
            statusMessage.textContent = message;
            statusMessage.className = `status-message status-${type}`;
            statusMessage.style.display = 'block';
            
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 5000);
        }
        
        function setLoading(button, loadingId, textId, isLoading) {
            const loading = document.getElementById(loadingId);
            const text = document.getElementById(textId);
            
            if (isLoading) {
                loading.classList.add('show');
                text.style.display = 'none';
                button.disabled = true;
            } else {
                loading.classList.remove('show');
                text.style.display = 'inline';
                button.disabled = false;
            }
        }
        
        function updateAttemptsInfo(remaining) {
            if (remaining > 0) {
                attemptsInfo.textContent = `Tentativi rimanenti: ${remaining}`;
                attemptsInfo.style.display = 'block';
            } else {
                attemptsInfo.style.display = 'none';
            }
        }
        
        function startCountdown(expiryTime) {
            // Implementazione countdown se necessaria
            // Per ora mostriamo solo l'orario di scadenza
            countdownDiv.textContent = `Codice scade alle: ${expiryTime}`;
            countdownDiv.style.display = 'block';
        }
    </script>
</body>
</html>
