/**
 * JavaScript per la gestione della lista viaggi
 */

document.addEventListener('DOMContentLoaded', function() {
    // Carica i dati per le dropdown quando si apre il modal
    const nuovoViaggioModal = document.getElementById('nuovoViaggioModal');
    const portoGestioneSelect = document.getElementById('porto_gestione_id');
    const naveSelect = document.getElementById('nave_id');
    const viaggioPreview = document.getElementById('viaggio_preview');
    const nuovoViaggioForm = document.getElementById('nuovoViaggioForm');

    /**
     * Pulisce i campi informativi di nave e porto
     */
    function pulisciCampiInformativi() {
        const naveInfoContainer = document.getElementById('nave-info');
        const portoInfoContainer = document.getElementById('porto-info');

        if (naveInfoContainer) {
            naveInfoContainer.innerHTML = '';
        }
        if (portoInfoContainer) {
            portoInfoContainer.innerHTML = '';
        }

        console.log('🧹 Campi informativi puliti');
    }

    // Carica dati quando si apre il modal
    nuovoViaggioModal.addEventListener('show.bs.modal', function() {
        console.log('🚀 Apertura modale nuovo viaggio...');

        // Pulisci i campi informativi all'apertura del modale
        pulisciCampiInformativi();
        ultimoPrefissoImpostato = ''; // Reset del prefisso tracciato

        caricaPortiGestione();
        caricaNavi();

        // Aggiungi un piccolo delay per assicurarsi che il DOM sia pronto
        setTimeout(() => {
            console.log('⏰ Caricamento porti ATLAS con delay...');
            caricaPortiAtlas();
        }, 100);
    });

    // Carica anche quando la modale è completamente mostrata
    nuovoViaggioModal.addEventListener('shown.bs.modal', function() {
        console.log('✅ Modale nuovo viaggio completamente mostrata');

        // Verifica che gli elementi esistano
        const portoArrivoSelect = document.getElementById('porto_arrivo');
        const portoDestinazioneSelect = document.getElementById('porto_destinazione');

        console.log('🔍 Verifica elementi dopo shown:', {
            portoArrivoSelect: !!portoArrivoSelect,
            portoDestinazioneSelect: !!portoDestinazioneSelect,
            portoArrivoOptions: portoArrivoSelect ? portoArrivoSelect.options.length : 0,
            portoDestinazioneOptions: portoDestinazioneSelect ? portoDestinazioneSelect.options.length : 0
        });

        // Se gli elementi esistono ma sono vuoti, ricarica i porti
        if (portoArrivoSelect && portoArrivoSelect.options.length <= 1) {
            console.log('🔄 Ricaricamento porti ATLAS perché dropdown vuote...');
            caricaPortiAtlas();
        }

    });

    // Suggerisce il prefisso viaggio quando cambia la nave (ma rimane editabile)
    let ultimoPrefissoImpostato = ''; // Traccia l'ultimo prefisso impostato automaticamente

    naveSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];

        if (selectedOption.value) {
            const prefissoViaggio = selectedOption.dataset.prefisso || '';
            const naveTSL = selectedOption.dataset.tsl || '';

            // Aggiorna il codice viaggio se:
            // 1. Il campo è vuoto, OPPURE
            // 2. Il valore attuale è uguale all'ultimo prefisso impostato automaticamente
            if (!viaggioPreview.value || viaggioPreview.value === ultimoPrefissoImpostato) {
                console.log('🚢 Aggiornamento automatico codice viaggio:', prefissoViaggio);
                viaggioPreview.value = prefissoViaggio;
                ultimoPrefissoImpostato = prefissoViaggio;

                // Evidenzia visivamente che il campo è stato auto-aggiornato
                viaggioPreview.style.backgroundColor = '#e8f5e8';
                viaggioPreview.title = 'Codice auto-generato dal prefisso della nave';

                // Rimuovi l'evidenziazione dopo 2 secondi
                setTimeout(() => {
                    viaggioPreview.style.backgroundColor = '';
                    viaggioPreview.title = '';
                }, 2000);
            } else {
                console.log('⚠️ Codice viaggio non aggiornato - modificato manualmente dall\'utente');
            }

            // Aggiorna TSL se presente
            aggiornaInfoNave(selectedOption);
            // Aggiorna anche AGEMAR perché dipende sia dal porto che dalla nave
            aggiornaInfoPorto();
        } else {
            // Se nessuna nave è selezionata, pulisci il campo solo se era auto-generato
            if (viaggioPreview.value === ultimoPrefissoImpostato) {
                viaggioPreview.value = '';
                ultimoPrefissoImpostato = '';
            }
            // Pulisci anche le info della nave e del porto
            aggiornaInfoNave(null);
            aggiornaInfoPorto();
        }
    });

    // Gestione cambio porto di gestione per AGEMAR
    portoGestioneSelect.addEventListener('change', function() {
        aggiornaInfoPorto();
    });

    // Traccia le modifiche manuali dell'utente
    viaggioPreview.addEventListener('input', function() {
        // Se l'utente modifica manualmente il codice, non è più auto-generato
        if (this.value !== ultimoPrefissoImpostato) {
            console.log('✏️ Codice viaggio modificato manualmente dall\'utente');
            this.style.backgroundColor = '';
            this.title = '';
        }
    });

    /**
     * Aggiorna le informazioni della nave selezionata (TSL)
     */
    function aggiornaInfoNave(selectedOption) {
        const infoContainer = document.getElementById('nave-info');
        if (!infoContainer) return;

        if (selectedOption && selectedOption.value) {
            const naveTSL = selectedOption.dataset.tsl;
            const naveNome = selectedOption.textContent;

            if (naveTSL && naveTSL !== 'None' && naveTSL !== '') {
                infoContainer.innerHTML = `
                    <div class="alert alert-info alert-sm">
                        <i class="fas fa-ship me-2"></i>
                        <strong>${naveNome}</strong><br>
                        <small>TSL: ${parseFloat(naveTSL).toLocaleString('it-IT')} tonnellate</small>
                    </div>
                `;
                console.log('📊 TSL aggiornato:', naveTSL);
            } else {
                infoContainer.innerHTML = `
                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <small>TSL non disponibile per questa nave</small>
                    </div>
                `;
            }
        } else {
            infoContainer.innerHTML = '';
        }
    }

    /**
     * Aggiorna le informazioni del porto di gestione (AGEMAR)
     */
    function aggiornaInfoPorto() {
        const infoContainer = document.getElementById('porto-info');
        if (!infoContainer) return;

        const selectedPorto = portoGestioneSelect.options[portoGestioneSelect.selectedIndex];
        const selectedNave = naveSelect.options[naveSelect.selectedIndex];

        if (selectedPorto && selectedPorto.value && selectedNave && selectedNave.value) {
            const portoAgemar = selectedPorto.dataset.agemar;
            const portoNome = selectedPorto.textContent;
            const agemarSalerno = selectedNave.dataset.agemarSalerno;
            const agemarGioia = selectedNave.dataset.agemarGioiaTauro;

            let agemarValue = null;
            let portoTipo = '';

            // Usa il campo AGEMAR del porto se disponibile
            if (portoAgemar) {
                if (portoAgemar === 'Agemar_Salerno') {
                    agemarValue = agemarSalerno;
                    portoTipo = 'Salerno';
                } else if (portoAgemar === 'Agemar_GioiaTauro') {
                    agemarValue = agemarGioia;
                    portoTipo = 'Gioia Tauro';
                }
            } else {
                // Fallback al nome del porto (compatibilità)
                const portoNomeLower = portoNome.toLowerCase();
                if (portoNomeLower.includes('salerno')) {
                    agemarValue = agemarSalerno;
                    portoTipo = 'Salerno';
                } else if (portoNomeLower.includes('gioia') || portoNomeLower.includes('tauro')) {
                    agemarValue = agemarGioia;
                    portoTipo = 'Gioia Tauro';
                }
            }

            if (agemarValue && agemarValue !== 'None' && agemarValue !== '') {
                infoContainer.innerHTML = `
                    <div class="alert alert-success alert-sm">
                        <i class="fas fa-euro-sign me-2"></i>
                        <strong>AGEMAR ${portoTipo}</strong><br>
                        <small>€ ${parseFloat(agemarValue).toLocaleString('it-IT', {minimumFractionDigits: 2})}</small>
                    </div>
                `;
                console.log('💰 AGEMAR aggiornato:', agemarValue, 'per', portoTipo);
            } else {
                infoContainer.innerHTML = `
                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <small>AGEMAR non disponibile per questo porto/nave</small>
                    </div>
                `;
            }
        } else {
            infoContainer.innerHTML = '';
        }
    }

    // Gestione submit form
    nuovoViaggioForm.addEventListener('submit', function(e) {
        e.preventDefault();
        creaViaggio();
    });

    /**
     * Carica la lista dei porti di gestione
     */
    async function caricaPortiGestione() {
        try {
            const response = await fetch('/api/porti-gestione');
            const data = await response.json();

            if (data.success) {
                portoGestioneSelect.innerHTML = '<option value="">Seleziona porto...</option>';
                data.data.forEach(porto => {
                    const option = document.createElement('option');
                    option.value = porto.id;
                    option.textContent = porto.nome;
                    option.dataset.agemar = porto.agemar || '';
                    portoGestioneSelect.appendChild(option);
                });
            } else {
                console.error('Errore nel caricamento porti:', data.error);
                mostraErrore('Errore nel caricamento dei porti di gestione');
            }
        } catch (error) {
            console.error('Errore nella richiesta porti:', error);
            mostraErrore('Errore di connessione nel caricamento dei porti');
        }
    }

    /**
     * Carica la lista delle navi
     */
    async function caricaNavi() {
        try {
            const response = await fetch('/api/navi');
            const data = await response.json();

            if (data.success) {
                naveSelect.innerHTML = '<option value="">Seleziona nave...</option>';
                data.data.forEach(nave => {
                    const option = document.createElement('option');
                    option.value = nave.id;
                    option.textContent = nave.nome;
                    option.dataset.prefisso = nave.prefisso_viaggio;
                    option.dataset.tsl = nave.TSL || '';
                    option.dataset.agemarSalerno = nave.agemar_salerno || '';
                    option.dataset.agemarGioiaTauro = nave.agemar_gioia_tauro || '';
                    naveSelect.appendChild(option);
                });
                console.log('✅ Navi caricate con dati TSL e AGEMAR');
            } else {
                console.error('Errore nel caricamento navi:', data.error);
                mostraErrore('Errore nel caricamento delle navi');
            }
        } catch (error) {
            console.error('Errore nella richiesta navi:', error);
            mostraErrore('Errore di connessione nel caricamento delle navi');
        }
    }

    // Usa la funzione globale caricaPortiAtlas definita alla fine del file





    /**
     * Crea un nuovo viaggio
     */
    async function creaViaggio() {
        const formData = new FormData(nuovoViaggioForm);

        // DEBUG: Mostra tutti i dati del form
        console.log('🔍 DEBUG CREAZIONE VIAGGIO:');
        console.log('Form data entries:');
        for (let [key, value] of formData.entries()) {
            console.log(`   ${key}: "${value}"`);
        }

        // Verifica specificamente i porti
        const portoArrivo = formData.get('porto_arrivo');
        const portoDestinazione = formData.get('porto_destinazione');
        console.log('🚢 Porti nel form:');
        console.log(`   Porto Arrivo: "${portoArrivo}"`);
        console.log(`   Porto Destinazione: "${portoDestinazione}"`);

        // PATCH: Se i porti sono vuoti, prova a ottenerli direttamente dagli elementi
        if (!portoArrivo || !portoDestinazione) {
            console.log('⚠️ Porti vuoti nel FormData, provo a ottenerli direttamente...');

            const portoArrivoElement = document.getElementById('porto_arrivo');
            const portoDestinazioneElement = document.getElementById('porto_destinazione');

            if (portoArrivoElement && portoArrivoElement.value) {
                console.log(`🔧 Porto arrivo da elemento: "${portoArrivoElement.value}"`);
                formData.set('porto_arrivo', portoArrivoElement.value);
            }

            if (portoDestinazioneElement && portoDestinazioneElement.value) {
                console.log(`🔧 Porto destinazione da elemento: "${portoDestinazioneElement.value}"`);
                formData.set('porto_destinazione', portoDestinazioneElement.value);
            }

            // Verifica di nuovo
            console.log('🔄 Verifica dopo patch:');
            console.log(`   Porto Arrivo: "${formData.get('porto_arrivo')}"`);
            console.log(`   Porto Destinazione: "${formData.get('porto_destinazione')}"`);
        }

        try {
            // Mostra loading
            const submitBtn = nuovoViaggioForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Creazione...';
            submitBtn.disabled = true;

            const response = await fetch('/api/viaggi', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                // Chiudi modal
                const modal = bootstrap.Modal.getInstance(nuovoViaggioModal);
                modal.hide();

                mostraSuccesso('Viaggio creato con successo!');

                // Ricarica la pagina per mostrare il nuovo viaggio
                // (per semplicità, dato che non abbiamo l'ID del nuovo viaggio nella risposta)
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                mostraErrore(data.error || 'Errore nella creazione del viaggio');
            }
        } catch (error) {
            console.error('Errore nella creazione viaggio:', error);
            mostraErrore('Errore di connessione nella creazione del viaggio');
        } finally {
            // Ripristina il pulsante
            const submitBtn = nuovoViaggioForm.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }



    // Reset form quando si chiude il modal
    nuovoViaggioModal.addEventListener('hidden.bs.modal', function() {
        nuovoViaggioForm.reset();
        viaggioPreview.value = '';
        ultimoPrefissoImpostato = ''; // Reset del prefisso tracciato

        // Pulisci anche i campi informativi
        pulisciCampiInformativi();
    });

    // Gestione modal modifica viaggio
    const modificaViaggioModal = document.getElementById('modificaViaggioModal');
    const modificaViaggioForm = document.getElementById('modificaViaggioForm');
    const modificaNaveSelect = document.getElementById('modifica_nave_id');
    const modificaViaggioPreview = document.getElementById('modifica_viaggio_preview');

    // Suggerisce il prefisso viaggio quando cambia la nave nel modal modifica (ma rimane editabile)
    let ultimoPrefissoImpostatoModifica = ''; // Traccia l'ultimo prefisso impostato automaticamente nella modifica
    let codiceViaggioOriginaleModifica = ''; // Traccia il codice viaggio originale caricato dal database
    let utenteHaModificatoManualmenteModifica = false; // Traccia se l'utente ha modificato manualmente
    let staImpostandoValoriIniziali = false; // Flag per evitare auto-aggiornamenti durante l'impostazione iniziale

    // Event listener per cambio nave - RIMOSSO perché ora viene gestito dinamicamente
    // nella funzione impostaNaveConVerifica per evitare interferenze durante l'impostazione iniziale

    // Traccia le modifiche manuali dell'utente nella modifica
    modificaViaggioPreview.addEventListener('input', function() {
        // Se l'utente modifica manualmente il codice, non è più auto-generato
        if (this.value !== ultimoPrefissoImpostatoModifica && this.value !== codiceViaggioOriginaleModifica) {
            console.log('✏️ Codice viaggio (modifica) modificato manualmente dall\'utente');
            console.log('   Nuovo valore:', this.value);
            console.log('   Ultimo prefisso:', ultimoPrefissoImpostatoModifica);
            console.log('   Codice originale:', codiceViaggioOriginaleModifica);

            utenteHaModificatoManualmenteModifica = true;
            this.style.backgroundColor = '';
            this.title = '';
        }
    });

    // Gestione submit form modifica
    modificaViaggioForm.addEventListener('submit', function(e) {
        e.preventDefault();
        modificaViaggioSubmit();
    });

    // Pulsante per forzare l'aggiornamento del codice viaggio
    const aggiornaCodiceBtn = document.getElementById('aggiorna_codice_modifica');
    if (aggiornaCodiceBtn) {
        aggiornaCodiceBtn.addEventListener('click', function() {
            const naveSelect = document.getElementById('modifica_nave_id');
            const selectedOption = naveSelect.options[naveSelect.selectedIndex];

            if (selectedOption.value) {
                const prefissoViaggio = selectedOption.dataset.prefisso || '';

                console.log('🔄 Aggiornamento FORZATO codice viaggio (modifica):', prefissoViaggio);

                modificaViaggioPreview.value = prefissoViaggio;
                ultimoPrefissoImpostatoModifica = prefissoViaggio;
                utenteHaModificatoManualmenteModifica = false;

                // Evidenzia visivamente che il campo è stato aggiornato
                modificaViaggioPreview.style.backgroundColor = '#fff3cd';
                modificaViaggioPreview.style.border = '2px solid #ffc107';
                aggiornaCodiceBtn.innerHTML = '<i class="fas fa-check me-1"></i>Aggiornato!';
                aggiornaCodiceBtn.className = 'btn btn-sm btn-success ms-2';

                // Ripristina dopo 2 secondi
                setTimeout(() => {
                    modificaViaggioPreview.style.backgroundColor = '';
                    modificaViaggioPreview.style.border = '';
                    aggiornaCodiceBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Auto-aggiorna';
                    aggiornaCodiceBtn.className = 'btn btn-sm btn-outline-primary ms-2';
                }, 2000);
            } else {
                console.log('⚠️ Nessuna nave selezionata per aggiornamento codice');

                // Evidenzia che serve selezionare una nave
                naveSelect.style.border = '2px solid #dc3545';
                aggiornaCodiceBtn.innerHTML = '<i class="fas fa-exclamation me-1"></i>Seleziona nave';
                aggiornaCodiceBtn.className = 'btn btn-sm btn-danger ms-2';

                setTimeout(() => {
                    naveSelect.style.border = '';
                    aggiornaCodiceBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Auto-aggiorna';
                    aggiornaCodiceBtn.className = 'btn btn-sm btn-outline-primary ms-2';
                }, 2000);
            }
        });
    }

    // Reset tracking quando si chiude il modale di modifica
    modificaViaggioModal.addEventListener('hidden.bs.modal', function() {
        ultimoPrefissoImpostatoModifica = '';
        codiceViaggioOriginaleModifica = '';
        utenteHaModificatoManualmenteModifica = false;
        staImpostandoValoriIniziali = false; // Reset anche questo flag
        console.log('🔄 Reset completo tracking alla chiusura modale modifica');
    });

    // Gestione modal elimina viaggio
    const eliminaViaggioModal = document.getElementById('eliminaViaggioModal');
    const confermaEliminaBtn = document.getElementById('confermaEliminaViaggio');

    confermaEliminaBtn.addEventListener('click', function() {
        if (viaggioIdDaEliminare) {
            eliminaViaggioSubmit(viaggioIdDaEliminare);
        }
    });
});

/**
 * Apre il modal per modificare un viaggio
 */
async function modificaViaggio(viaggioId) {
    try {
        console.log('🚀 Caricamento dati viaggio:', viaggioId);

        // Carica i dati del viaggio dal server
        const response = await fetch(`/api/viaggi/${viaggioId}`);
        const data = await response.json();

        if (!data.success) {
            mostraErrore('Errore nel caricamento dei dati del viaggio');
            return;
        }

        const viaggio = data.data;
        console.log('📋 Dati viaggio caricati:', viaggio);

        // Imposta l'ID del viaggio da modificare
        document.getElementById('modifica_viaggio_id').value = viaggio.id;

        // RESET: Resetta le variabili di tracking per permettere l'auto-aggiornamento
        ultimoPrefissoImpostatoModifica = '';
        codiceViaggioOriginaleModifica = viaggio.viaggio || '';
        utenteHaModificatoManualmenteModifica = false;

        // Carica le dropdown e poi imposta i valori CON SINCRONIZZAZIONE MIGLIORATA
        console.log('🔄 Caricamento dropdown per modifica...');

        // Carica tutto in parallelo per velocizzare
        const [portiGestioneOk, naviOk, portiAtlasOk] = await Promise.all([
            caricaPortiGestioneModifica(),
            caricaNaviModifica(),
            caricaPortiAtlas()
        ]);

        console.log('✅ Risultati caricamento:', {
            portiGestione: portiGestioneOk,
            navi: naviOk,
            portiAtlas: portiAtlasOk
        });

        // Verifica che le dropdown siano state caricate correttamente
        const naveSelect = document.getElementById('modifica_nave_id');
        const portoGestioneSelect = document.getElementById('modifica_porto_gestione_id');

        console.log('🔍 Verifica dropdown caricate:');
        console.log('   Navi disponibili:', naveSelect.options.length);
        console.log('   Porti gestione disponibili:', portoGestioneSelect.options.length);

        // Se le dropdown non sono caricate, aspetta un po' di più
        if (naveSelect.options.length <= 1 || portoGestioneSelect.options.length <= 1) {
            console.log('⏳ Dropdown non completamente caricate, attendo...');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // IMPOSTAZIONE VALORI CON VERIFICA ROBUSTA
        console.log('🔧 Impostazione valori nel modale modifica...');
        console.log('   Porto Gestione ID atteso:', viaggio.porto_gestione_id);
        console.log('   Nave ID atteso:', viaggio.nave_id);
        console.log('   Codice Viaggio Originale:', viaggio.viaggio);

        // Imposta porto di gestione
        portoGestioneSelect.value = viaggio.porto_gestione_id || '';
        console.log('   Porto Gestione impostato:', portoGestioneSelect.value);

        // Imposta nave con verifica multipla (disabilita temporaneamente l'auto-aggiornamento)
        staImpostandoValoriIniziali = true;
        await impostaNaveConVerifica(naveSelect, viaggio.nave_id, viaggio.nome_nave);
        staImpostandoValoriIniziali = false;

        // Imposta altri campi
        document.getElementById('modifica_viaggio_preview').value = viaggio.viaggio || '';
        document.getElementById('modifica_data_arrivo').value = formatDateForInput(viaggio.data_arrivo);
        document.getElementById('modifica_data_partenza').value = formatDateForInput(viaggio.data_partenza);

        console.log('🔄 Reset tracking modifica - codice viaggio ora può essere auto-aggiornato');

        // Imposta i porti se presenti (con delay per assicurarsi che le dropdown siano pronte)
        setTimeout(() => {
            if (viaggio.porto_arrivo) {
                const portoArrivoSelect = document.getElementById('modifica_porto_arrivo');
                if (portoArrivoSelect) {
                    portoArrivoSelect.value = viaggio.porto_arrivo;
                    console.log('🚢 Porto arrivo impostato:', viaggio.porto_arrivo);
                }
            }
            if (viaggio.porto_destinazione) {
                const portoDestinazioneSelect = document.getElementById('modifica_porto_destinazione');
                if (portoDestinazioneSelect) {
                    portoDestinazioneSelect.value = viaggio.porto_destinazione;
                    console.log('🏁 Porto destinazione impostato:', viaggio.porto_destinazione);
                }
            }
        }, 200);

        // Mostra il modal
        const modal = new bootstrap.Modal(document.getElementById('modificaViaggioModal'));
        modal.show();

        console.log('✅ Modal di modifica aperto con successo');

    } catch (error) {
        console.error('❌ Errore nel caricamento viaggio:', error);
        mostraErrore('Errore nel caricamento dei dati del viaggio');
    }
}

/**
 * Funzione helper per impostare la nave con verifica robusta
 */
async function impostaNaveConVerifica(naveSelect, naveIdAtteso, nomeNaveAtteso) {
    console.log('🚢 Impostazione nave con verifica robusta...');
    console.log('   ID atteso:', naveIdAtteso, '(tipo:', typeof naveIdAtteso, ')');
    console.log('   Nome atteso:', nomeNaveAtteso);

    // DISABILITA TEMPORANEAMENTE TUTTI GLI EVENT LISTENER
    const originalOnChange = naveSelect.onchange;
    naveSelect.onchange = null;

    // Rimuovi tutti gli event listener clonando l'elemento
    const nuovoSelect = naveSelect.cloneNode(true);
    naveSelect.parentNode.replaceChild(nuovoSelect, naveSelect);

    // Aggiorna il riferimento
    const selectElement = document.getElementById('modifica_nave_id');

    // Analizza le opzioni disponibili PRIMA di impostare
    const opzioni = Array.from(selectElement.options);
    console.log('📋 Opzioni disponibili nella dropdown:');
    opzioni.forEach((option, index) => {
        const isTarget = (option.value === String(naveIdAtteso));
        console.log(`   ${index}: value="${option.value}" (tipo: ${typeof option.value}), text="${option.textContent}"${isTarget ? ' ⭐ TARGET' : ''}`);
    });

    // Trova l'opzione corretta
    let opzioneCorretta = null;

    // 1. Cerca per ID esatto (string comparison)
    opzioneCorretta = opzioni.find(option => option.value === String(naveIdAtteso));

    if (!opzioneCorretta && naveIdAtteso) {
        // 2. Cerca per ID numerico
        opzioneCorretta = opzioni.find(option => parseInt(option.value) === parseInt(naveIdAtteso));
    }

    if (!opzioneCorretta && nomeNaveAtteso) {
        // 3. Cerca per nome
        opzioneCorretta = opzioni.find(option =>
            option.textContent.trim().toLowerCase() === nomeNaveAtteso.toLowerCase()
        );
    }

    if (opzioneCorretta) {
        console.log(`✅ Opzione target trovata: value="${opzioneCorretta.value}", text="${opzioneCorretta.textContent}"`);

        // METODO ROBUSTO: Imposta selectedIndex invece di value
        const targetIndex = Array.from(selectElement.options).indexOf(opzioneCorretta);
        console.log(`🎯 Impostazione selectedIndex: ${targetIndex}`);

        selectElement.selectedIndex = targetIndex;

        // Verifica doppia
        const valoreImpostato = selectElement.value;
        const opzioneSelezionata = selectElement.options[selectElement.selectedIndex];

        console.log(`🔍 Verifica impostazione:`);
        console.log(`   selectedIndex: ${selectElement.selectedIndex}`);
        console.log(`   value: "${valoreImpostato}"`);
        console.log(`   opzione selezionata: "${opzioneSelezionata.textContent}"`);

        if (valoreImpostato === String(naveIdAtteso)) {
            console.log('🎉 SUCCESSO: Nave impostata correttamente!');

            // Ripristina gli event listener dopo un delay
            setTimeout(() => {
                console.log('🔄 Ripristino event listener...');
                // Riattacca l'event listener per il cambio nave
                const modificaNaveSelect = document.getElementById('modifica_nave_id');
                if (modificaNaveSelect) {
                    modificaNaveSelect.addEventListener('change', function() {
                        // Se stiamo impostando i valori iniziali, non fare nulla
                        if (staImpostandoValoriIniziali) {
                            console.log('🔄 Cambio nave durante impostazione iniziale - ignorato');
                            return;
                        }

                        const selectedOption = this.options[this.selectedIndex];
                        console.log('🚢 Cambio nave utente:', {
                            valore: selectedOption.value,
                            testo: selectedOption.textContent,
                            prefisso: selectedOption.dataset.prefisso
                        });

                        if (selectedOption.value) {
                            const prefissoViaggio = selectedOption.dataset.prefisso || '';

                            // Aggiorna il codice viaggio se necessario
                            const modificaViaggioPreview = document.getElementById('modifica_viaggio_preview');
                            const valoreAttuale = modificaViaggioPreview.value;
                            const puo_aggiornare = (
                                !valoreAttuale ||
                                valoreAttuale === ultimoPrefissoImpostatoModifica ||
                                (valoreAttuale === codiceViaggioOriginaleModifica && !utenteHaModificatoManualmenteModifica)
                            );

                            if (puo_aggiornare) {
                                console.log('🚢 Aggiornamento automatico codice viaggio (modifica):', prefissoViaggio);
                                modificaViaggioPreview.value = prefissoViaggio;
                                ultimoPrefissoImpostatoModifica = prefissoViaggio;
                                utenteHaModificatoManualmenteModifica = false;

                                // Evidenzia visivamente
                                modificaViaggioPreview.style.backgroundColor = '#e8f5e8';
                                modificaViaggioPreview.title = 'Codice auto-generato dal prefisso della nave';

                                setTimeout(() => {
                                    modificaViaggioPreview.style.backgroundColor = '';
                                    modificaViaggioPreview.title = '';
                                }, 2000);
                            }
                        }
                    });
                }
            }, 500);

            return true;
        } else {
            console.log('❌ ERRORE: Impostazione fallita anche con selectedIndex');
            console.log(`   Atteso: "${naveIdAtteso}"`);
            console.log(`   Ottenuto: "${valoreImpostato}"`);
        }
    } else {
        console.log('❌ ERRORE: Opzione target non trovata');
        console.log(`   ID cercato: ${naveIdAtteso} (tipo: ${typeof naveIdAtteso})`);
        console.log(`   Nome cercato: ${nomeNaveAtteso}`);
    }

    // Se arriviamo qui, c'è stato un errore
    console.log('🚨 FALLBACK: Impostazione fallita, mostro avviso utente');

    if (typeof mostraErrore === 'function') {
        mostraErrore(`⚠️ ATTENZIONE: La nave "${nomeNaveAtteso}" non è stata selezionata correttamente. Verifica e seleziona manualmente la nave corretta prima di salvare.`);
    }

    return false;
}

// Variabile globale per l'ID del viaggio da eliminare
let viaggioIdDaEliminare = null;

/**
 * Apre il modal per eliminare un viaggio
 */
function eliminaViaggio(viaggioId, viaggioNome) {
    console.log('Elimina viaggio:', { viaggioId, viaggioNome });

    viaggioIdDaEliminare = viaggioId;
    document.getElementById('elimina_viaggio_nome').textContent = viaggioNome;

    const modal = new bootstrap.Modal(document.getElementById('eliminaViaggioModal'));
    modal.show();
}

/**
 * Carica i porti di gestione per il modal modifica
 */
async function caricaPortiGestioneModifica() {
    try {
        const response = await fetch('/api/porti-gestione');
        const data = await response.json();

        if (data.success) {
            const select = document.getElementById('modifica_porto_gestione_id');
            select.innerHTML = '<option value="">Seleziona porto...</option>';
            data.data.forEach(porto => {
                const option = document.createElement('option');
                option.value = porto.id;
                option.textContent = porto.nome;
                select.appendChild(option);
            });
            return true;
        }
        return false;
    } catch (error) {
        console.error('Errore nel caricamento porti per modifica:', error);
        return false;
    }
}

/**
 * Carica le navi per il modal modifica
 */
async function caricaNaviModifica() {
    try {
        console.log('🚢 Caricamento navi per modifica...');
        const response = await fetch('/api/navi');
        const data = await response.json();

        if (data.success && data.data) {
            const select = document.getElementById('modifica_nave_id');
            if (!select) {
                console.error('❌ Elemento modifica_nave_id non trovato');
                return false;
            }

            select.innerHTML = '<option value="">Seleziona nave...</option>';

            console.log(`📋 Caricamento ${data.data.length} navi...`);
            data.data.forEach((nave, index) => {
                const option = document.createElement('option');
                option.value = nave.id;
                option.textContent = nave.nome;
                option.dataset.prefisso = nave.prefisso_viaggio || '';
                option.dataset.tsl = nave.TSL || '';
                option.dataset.agemarSalerno = nave.agemar_salerno || '';
                option.dataset.agemarGioiaTauro = nave.agemar_gioia_tauro || '';
                select.appendChild(option);

                // Log dettagliato per debug
                if (index < 5) { // Solo le prime 5 per non intasare i log
                    console.log(`   ${index + 1}. ID: ${nave.id}, Nome: "${nave.nome}", Prefisso: "${nave.prefisso_viaggio}", TSL: ${nave.TSL}`);
                }
            });

            console.log(`✅ Caricate ${data.data.length} navi nella dropdown modifica`);
            console.log(`   Opzioni totali nella select: ${select.options.length}`);

            return true;
        } else {
            console.error('❌ Dati navi non validi:', data);
            return false;
        }
    } catch (error) {
        console.error('❌ Errore nel caricamento navi per modifica:', error);
        return false;
    }
}

/**
 * Invia la modifica del viaggio
 */
async function modificaViaggioSubmit() {
    console.log('Inizio modifica viaggio submit');

    const formData = new FormData(document.getElementById('modificaViaggioForm'));
    const viaggioId = formData.get('viaggio_id');

    console.log('Viaggio ID:', viaggioId);
    console.log('Form data:', Object.fromEntries(formData));

    try {
        const submitBtn = document.querySelector('#modificaViaggioForm button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Salvando...';
        submitBtn.disabled = true;

        const response = await fetch(`/api/viaggi/${viaggioId}/update`, {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        console.log('Risposta modifica viaggio:', data);

        if (data.success) {
            // SOLUZIONE SEMPLICE E DEFINITIVA: RELOAD IMMEDIATO
            console.log('✅ VIAGGIO MODIFICATO - RELOAD IMMEDIATO DELLA PAGINA');

            // Chiudi il modal immediatamente
            const modal = bootstrap.Modal.getInstance(document.getElementById('modificaViaggioModal'));
            if (modal) {
                modal.hide();
            }

            // Mostra messaggio veloce e ricarica subito
            mostraSuccesso('Viaggio modificato! Ricaricamento...');

            // RELOAD IMMEDIATO - NESSUN DELAY
            setTimeout(() => {
                console.log('🔄 RICARICO LA PAGINA ADESSO');
                window.location.reload(true);
            }, 300); // Solo 0.3 secondi per vedere il messaggio
        } else {
            mostraErrore(data.error || 'Errore nella modifica del viaggio');
        }
    } catch (error) {
        console.error('Errore nella modifica viaggio:', error);
        mostraErrore('Errore di connessione nella modifica del viaggio');
    } finally {
        const submitBtn = document.querySelector('#modificaViaggioForm button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> Salva Modifiche';
        submitBtn.disabled = false;
    }
}

/**
 * Elimina il viaggio
 */
async function eliminaViaggioSubmit(viaggioId) {
    try {
        console.log('Eliminazione viaggio ID:', viaggioId);

        const submitBtn = document.getElementById('confermaEliminaViaggio');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Eliminando...';
        submitBtn.disabled = true;

        const response = await fetch(`/api/viaggi/${viaggioId}`, {
            method: 'DELETE'
        });

        const data = await response.json();
        console.log('Risposta eliminazione:', data);

        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('eliminaViaggioModal'));
            modal.hide();

            mostraSuccesso('Viaggio eliminato con successo!');

            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            mostraErrore(data.error || 'Errore nell\'eliminazione del viaggio');
        }
    } catch (error) {
        console.error('Errore nell\'eliminazione viaggio:', error);
        mostraErrore('Errore di connessione nell\'eliminazione del viaggio');
    } finally {
        const submitBtn = document.getElementById('confermaEliminaViaggio');
        const originalText = '<i class="fas fa-trash me-1"></i>Elimina Definitivamente';
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * Aggiorna una riga specifica nella tabella dei viaggi
 */
async function aggiornaRigaViaggio(viaggioId) {
    try {
        console.log('Aggiornamento riga viaggio ID:', viaggioId);

        // 🚀 OTTIENI I DATI AGGIORNATI CON RISOLUZIONE NUCLEARE FORZATA + CACHE BUSTING
        const cacheBuster = Date.now();
        const response = await fetch(`/api/sof/da-realizzare/search?_t=${cacheBuster}`, {
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        const data = await response.json();

        console.log('Dati viaggio ricevuti:', data);

        if (data.success) {
            // 🔍 TROVA IL VIAGGIO SPECIFICO NEI DATI CON RISOLUZIONE NUCLEARE
            const viaggio = data.viaggi.find(v => v.id === parseInt(viaggioId));
            if (!viaggio) {
                console.error(`❌ Viaggio ${viaggioId} non trovato nei dati aggiornati`);
                console.log('📋 Viaggi disponibili:', data.viaggi.map(v => ({id: v.id, viaggio: v.viaggio})));
                // Prova a ricaricare la pagina se il viaggio non è trovato
                console.log('🔄 Fallback: ricarico la pagina per sincronizzare i dati');
                setTimeout(() => window.location.reload(true), 500);
                return;
            }
            console.log('Viaggio da aggiornare (con risoluzione nucleare):', viaggio);

            // 🎯 TROVA LA RIGA USANDO L'ATTRIBUTO DATA-VIAGGIO-ID
            const riga = document.querySelector(`tr[data-viaggio-id="${viaggioId}"]`);

            if (riga) {
                console.log('🎯 Riga trovata, aggiornamento con risoluzione nucleare in corso...');

                // 🚀 AGGIORNA LE CELLE USANDO I SELETTORI CORRETTI
                console.log(`📋 Aggiornamento celle per viaggio ${viaggioId}:`, viaggio);

                // 🔄 AGGIORNA CODICE VIAGGIO (prima colonna - badge)
                const badgeViaggio = riga.querySelector('td:nth-child(1) .badge');
                if (badgeViaggio && viaggio.viaggio) {
                    badgeViaggio.textContent = viaggio.viaggio;
                    console.log(`   ✅ Codice viaggio aggiornato: '${viaggio.viaggio}'`);
                }

                // 🔄 AGGIORNA NOME NAVE (seconda colonna - .cell-nave)
                const cellaNave = riga.querySelector('.cell-nave strong');
                if (cellaNave && viaggio.nome_nave) {
                    cellaNave.textContent = viaggio.nome_nave;
                    console.log(`   ✅ Nome nave aggiornato: '${viaggio.nome_nave}'`);
                }

                // 🔄 AGGIORNA PORTO DI GESTIONE (terza colonna - badge porto)
                const badgePorto = riga.querySelector('td:nth-child(3) .porto-gestione-badge');
                if (badgePorto && viaggio.nome_porto) {
                    // Mantieni l'icona e aggiorna solo il testo
                    badgePorto.innerHTML = `<i class="fas fa-anchor me-1"></i>${viaggio.nome_porto}`;
                    badgePorto.title = viaggio.nome_porto;
                    console.log(`   ✅ Porto gestione aggiornato: '${viaggio.nome_porto}'`);
                }

                // 🔄 AGGIORNA PORTO ARRIVO (quarta colonna - .cell-porto-arrivo)
                const cellaPortoArrivo = riga.querySelector('.cell-porto-arrivo');
                if (cellaPortoArrivo) {
                    if (viaggio.porto_arrivo_nome) {
                        cellaPortoArrivo.innerHTML = `<span class="badge bg-info text-dark">${viaggio.porto_arrivo_nome}</span>`;
                        cellaPortoArrivo.title = `Codice: ${viaggio.porto_arrivo_code || 'N/A'}`;
                        console.log(`   ✅ Porto arrivo aggiornato: '${viaggio.porto_arrivo_nome}'`);
                    } else {
                        cellaPortoArrivo.innerHTML = `<span class="text-muted">N/A</span>`;
                        cellaPortoArrivo.title = "Codice: N/A";
                    }
                }

                // 🔄 AGGIORNA PORTO DESTINAZIONE (quinta colonna - .cell-porto-destinazione)
                const cellaPortoDestinazione = riga.querySelector('.cell-porto-destinazione');
                if (cellaPortoDestinazione) {
                    if (viaggio.porto_destinazione_nome) {
                        cellaPortoDestinazione.innerHTML = `<span class="badge bg-warning text-dark">${viaggio.porto_destinazione_nome}</span>`;
                        cellaPortoDestinazione.title = `Codice: ${viaggio.porto_destinazione_code || 'N/A'}`;
                        console.log(`   ✅ Porto destinazione aggiornato: '${viaggio.porto_destinazione_nome}'`);
                    } else {
                        cellaPortoDestinazione.innerHTML = `<span class="text-muted">N/A</span>`;
                        cellaPortoDestinazione.title = "Codice: N/A";
                    }
                }

                // 🔄 AGGIORNA DATA ARRIVO (sesta colonna)
                const cellaDataArrivo = riga.querySelector('td:nth-child(6)');
                if (cellaDataArrivo && viaggio.data_arrivo) {
                    const dataArrivo = new Date(viaggio.data_arrivo);
                    const dataFormattata = dataArrivo.toLocaleDateString('it-IT');
                    cellaDataArrivo.innerHTML = `<span class="text-success fw-bold">${dataFormattata}</span>`;
                    console.log(`   ✅ Data arrivo aggiornata: '${dataFormattata}'`);
                } else if (cellaDataArrivo) {
                    cellaDataArrivo.innerHTML = `<span class="text-muted">N/A</span>`;
                }

                // 🔄 AGGIORNA DATA PARTENZA (settima colonna)
                const cellaDataPartenza = riga.querySelector('td:nth-child(7)');
                if (cellaDataPartenza && viaggio.data_partenza) {
                    const dataPartenza = new Date(viaggio.data_partenza);
                    const dataFormattata = dataPartenza.toLocaleDateString('it-IT');
                    cellaDataPartenza.innerHTML = `<span class="text-warning fw-bold">${dataFormattata}</span>`;
                    console.log(`   ✅ Data partenza aggiornata: '${dataFormattata}'`);
                } else if (cellaDataPartenza) {
                    cellaDataPartenza.innerHTML = `<span class="text-muted">N/A</span>`;
                }

                // 🎨 ANIMAZIONE DI AGGIORNAMENTO
                riga.style.backgroundColor = '#d4edda'; // Verde chiaro per indicare aggiornamento
                setTimeout(() => {
                    riga.style.backgroundColor = '';
                }, 2000);

                console.log('✅ RIGA AGGIORNATA CON RISOLUZIONE NUCLEARE COMPLETATA');
            } else {
                console.error(`❌ Riga viaggio ${viaggioId} non trovata nel DOM`);
                throw new Error(`Riga viaggio ${viaggioId} non trovata`);
            }
        } else {
            console.error('Errore nei dati ricevuti:', data.error);
            // Non ricaricare la pagina per evitare loop infinito
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore nell\'aggiornamento dei dati del viaggio');
            }
            throw new Error(data.error || 'Errore nei dati ricevuti');
        }
    } catch (error) {
        console.error('Errore nell\'aggiornamento della riga:', error);
        // Non ricaricare la pagina per evitare loop infinito
        if (typeof mostraErrore === 'function') {
            mostraErrore('Errore nell\'aggiornamento della riga del viaggio');
        }
        throw error; // Rilancia l'errore per permettere al chiamante di gestirlo
    }
}

/**
 * Formatta una data per la visualizzazione (DD/MM/YYYY)
 */
function formatDateForDisplay(dateString) {
    if (!dateString || dateString === 'None' || dateString === 'null') return 'N/A';

    try {
        const date = new Date(dateString);
        if (!isNaN(date.getTime())) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }
        return dateString;
    } catch (error) {
        console.error('Errore nel formato data per visualizzazione:', error, dateString);
        return dateString;
    }
}

/**
 * Formatta una data per l'input date
 */
function formatDateForInput(dateString) {
    if (!dateString || dateString === 'None' || dateString === 'null') return '';

    try {
        // Se la data è nel formato DD/MM/YYYY, convertila in YYYY-MM-DD
        if (dateString.includes('/')) {
            const parts = dateString.split('/');
            if (parts.length === 3) {
                return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
            }
        }

        // Se è nel formato YYYY-MM-DD o simile
        if (dateString.includes('-')) {
            return dateString.split('T')[0]; // Prende solo la parte della data
        }

        // Prova a parsare come oggetto Date
        const date = new Date(dateString);
        if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        return '';
    } catch (error) {
        console.error('Errore nel formato data:', error, dateString);
        return '';
    }
}

/**
 * 🎨 MESSAGGI SPETTACOLARI - Ora usano il sistema SNIP unificato
 * Le vecchie funzioni sono state sostituite automaticamente dal sistema messages.js
 */

// Le funzioni mostraSuccesso() e mostraErrore() sono ora gestite automaticamente
// dal sistema SNIP Messages caricato nella navbar. Non serve più codice qui!

// Se per qualche motivo il sistema non fosse caricato, fallback di sicurezza:
if (typeof window.snipMessages === 'undefined') {
    console.warn('⚠️ Sistema SNIP Messages non caricato, uso fallback');

    window.mostraSuccesso = function(messaggio) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `<i class="fas fa-check-circle me-2"></i>${messaggio}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
        document.body.appendChild(alert);
        setTimeout(() => alert.parentNode && alert.parentNode.removeChild(alert), 5000);
    };

    window.mostraErrore = function(messaggio) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${messaggio}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
        document.body.appendChild(alert);
        setTimeout(() => alert.parentNode && alert.parentNode.removeChild(alert), 7000);
    };
}

/**
 * Popola una select con i porti - COPIA ESATTA DA viaggio-dettaglio.js
 * Funzione globale per essere accessibile da tutte le parti del codice
 */
function popolaSelectPorti(selectElement, porti) {
    if (!selectElement) {
        console.warn('⚠️ Elemento select non trovato per popolamento porti');
        return;
    }

    selectElement.innerHTML = '<option value="">Seleziona porto...</option>';
    porti.forEach(porto => {
        const option = document.createElement('option');
        option.value = porto.id_cod;
        option.textContent = porto.porto; // Solo il nome del porto, come nel tab orari
        selectElement.appendChild(option);
    });

    console.log(`📊 Popolato ${selectElement.id} con ${porti.length} porti`);
}

/**
 * Carica i porti ATLAS - Funzione globale per essere accessibile da modificaViaggio
 */
async function caricaPortiAtlas() {
    try {
        console.log('🚢 Caricamento porti ATLAS (funzione globale)...');
        const response = await fetch('/api/atlas?limit=1000');
        const data = await response.json();

        if (data.success) {
            console.log(`📋 Trovati ${data.data.length} porti ATLAS`);

            // Trova gli elementi delle dropdown
            const portoArrivoSelect = document.getElementById('porto_arrivo');
            const portoDestinazioneSelect = document.getElementById('porto_destinazione');
            const modificaPortoArrivoSelect = document.getElementById('modifica_porto_arrivo');
            const modificaPortoDestinazioneSelect = document.getElementById('modifica_porto_destinazione');

            console.log('🔍 Elementi trovati:', {
                portoArrivoSelect: !!portoArrivoSelect,
                portoDestinazioneSelect: !!portoDestinazioneSelect,
                modificaPortoArrivoSelect: !!modificaPortoArrivoSelect,
                modificaPortoDestinazioneSelect: !!modificaPortoDestinazioneSelect
            });

            // Popola entrambe le dropdown usando la funzione globale
            if (portoArrivoSelect) {
                popolaSelectPorti(portoArrivoSelect, data.data);
                console.log(`✅ Popolato porto_arrivo con ${data.data.length} porti`);
            }

            if (portoDestinazioneSelect) {
                popolaSelectPorti(portoDestinazioneSelect, data.data);
                console.log(`✅ Popolato porto_destinazione con ${data.data.length} porti`);
            }

            // Popola anche le dropdown della modale di modifica
            if (modificaPortoArrivoSelect) {
                popolaSelectPorti(modificaPortoArrivoSelect, data.data);
                console.log(`✅ Popolato modifica_porto_arrivo con ${data.data.length} porti`);
            }

            if (modificaPortoDestinazioneSelect) {
                popolaSelectPorti(modificaPortoDestinazioneSelect, data.data);
                console.log(`✅ Popolato modifica_porto_destinazione con ${data.data.length} porti`);
            }

            console.log('🎯 Caricamento porti ATLAS completato!');
            return Promise.resolve();
        } else {
            console.error('Errore nel caricamento porti ATLAS:', data.error);
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore nel caricamento dei porti');
            }
            return Promise.reject(data.error);
        }
    } catch (error) {
        console.error('Errore nella richiesta porti ATLAS:', error);
        if (typeof mostraErrore === 'function') {
            mostraErrore('Errore di connessione nel caricamento dei porti');
        }
        return Promise.reject(error);
    }
}

/**
 * Aggiorna la tabella dopo una modifica viaggio con strategia multi-livello
 */
async function aggiornaTabellaDopModifica(viaggioId) {
    console.log(`🔄 Inizio aggiornamento tabella dopo modifica viaggio ${viaggioId}`);

    try {
        // STRATEGIA 1: Prova aggiornamento completo tabella
        console.log('📋 Strategia 1: Aggiornamento completo tabella');
        const success1 = await tentativoAggiornamentoCompleto(viaggioId);
        if (success1) {
            console.log('✅ Strategia 1 riuscita - Tabella aggiornata');
            return;
        }

        // STRATEGIA 2: Prova aggiornamento singola riga
        console.log('📋 Strategia 2: Aggiornamento singola riga');
        const success2 = await tentativoAggiornamentoSingolaRiga(viaggioId);
        if (success2) {
            console.log('✅ Strategia 2 riuscita - Riga aggiornata');
            return;
        }

        // STRATEGIA 3: Messaggio di errore invece di reload
        console.log('📋 Strategia 3: Mostra messaggio di errore (evita loop)');
        console.log('⚠️ Tutte le strategie di aggiornamento sono fallite');

        if (typeof mostraErrore === 'function') {
            mostraErrore('Impossibile aggiornare automaticamente la tabella. Aggiorna manualmente la pagina se necessario.');
        }

    } catch (error) {
        console.error('❌ Errore in aggiornaTabellaDopModifica:', error);
        // Non ricaricare automaticamente per evitare loop
        if (typeof mostraErrore === 'function') {
            mostraErrore('Errore nell\'aggiornamento della tabella. Aggiorna manualmente la pagina se necessario.');
        }
    }
}

/**
 * Tentativo 1: Aggiornamento completo della tabella
 */
async function tentativoAggiornamentoCompleto(viaggioId) {
    try {
        console.log('🔄 Tentativo aggiornamento completo tabella...');

        // Richiesta con cache-busting aggressivo
        const timestamp = Date.now();
        const response = await fetch(`/api/sof/da-realizzare/search?_t=${timestamp}&_cache_bust=${Math.random()}`, {
            method: 'GET',
            cache: 'no-store',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.viaggi && Array.isArray(data.viaggi)) {
            // Controlla se la funzione updateTableContent esiste
            if (typeof updateTableContent === 'function') {
                updateTableContent(data.viaggi);
                console.log(`✅ Tabella aggiornata con ${data.viaggi.length} viaggi`);

                // Evidenzia la riga modificata
                evidenziaRigaModificata(viaggioId);
                return true;
            } else {
                // Se updateTableContent non esiste, aggiorna manualmente
                console.log('⚠️ updateTableContent non disponibile, aggiorno manualmente');
                return aggiornaTabellaManualment(data.viaggi, viaggioId);
            }
        } else {
            throw new Error('Dati non validi ricevuti dal server');
        }

    } catch (error) {
        console.error('❌ Tentativo aggiornamento completo fallito:', error);
        return false;
    }
}

/**
 * Tentativo 2: Aggiornamento singola riga
 */
async function tentativoAggiornamentoSingolaRiga(viaggioId) {
    try {
        console.log(`🔄 Tentativo aggiornamento singola riga per viaggio ${viaggioId}...`);

        // Usa la funzione esistente aggiornaRigaViaggio
        if (typeof aggiornaRigaViaggio === 'function') {
            await aggiornaRigaViaggio(viaggioId);
            return true;
        } else {
            console.error('❌ Funzione aggiornaRigaViaggio non disponibile');
            return false;
        }

    } catch (error) {
        console.error('❌ Tentativo aggiornamento singola riga fallito:', error);
        return false;
    }
}

/**
 * Aggiorna la tabella manualmente ricostruendo l'HTML
 */
function aggiornaTabellaManualment(viaggi, viaggioModificato) {
    try {
        console.log('🔧 Aggiornamento manuale tabella...');

        const tbody = document.querySelector('table tbody');
        if (!tbody) {
            console.error('❌ Tbody non trovato');
            return false;
        }

        // Ricostruisci le righe della tabella
        let html = '';

        if (viaggi.length === 0) {
            html = `
                <tr>
                    <td colspan="8" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-search fa-3x mb-3 text-warning"></i>
                            <h5>Nessun viaggio trovato</h5>
                            <p>Non ci sono viaggi da visualizzare.</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            viaggi.forEach(viaggio => {
                const isModified = viaggio.id == viaggioModificato;
                const highlightClass = isModified ? 'style="background-color: #d4edda;"' : '';

                html += `
                    <tr data-viaggio-id="${viaggio.id}" ${highlightClass}>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    <i class="fas fa-route"></i>
                                </div>
                                <span class="badge bg-secondary">${viaggio.viaggio || 'N/A'}</span>
                            </div>
                        </td>
                        <td class="cell-nave">
                            <strong>${viaggio.nome_nave || 'N/A'}</strong>
                        </td>
                        <td>
                            ${viaggio.nome_porto ?
                                `<span class="badge bg-primary porto-gestione-badge" title="${viaggio.nome_porto}">
                                    <i class="fas fa-anchor me-1"></i>${viaggio.nome_porto}
                                </span>` :
                                `<span class="text-muted"><i class="fas fa-minus me-1"></i>N/A</span>`
                            }
                        </td>
                        <td class="cell-porto-arrivo">
                            ${viaggio.porto_arrivo_nome ?
                                `<span class="badge bg-info text-dark">${viaggio.porto_arrivo_nome}</span>` :
                                `<span class="text-muted">N/A</span>`
                            }
                        </td>
                        <td class="cell-porto-destinazione">
                            ${viaggio.porto_destinazione_nome ?
                                `<span class="badge bg-warning text-dark">${viaggio.porto_destinazione_nome}</span>` :
                                `<span class="text-muted">N/A</span>`
                            }
                        </td>
                        <td>
                            ${viaggio.data_arrivo ?
                                `<span class="text-success fw-bold">${formatDateForDisplay(viaggio.data_arrivo)}</span>` :
                                `<span class="text-muted">N/A</span>`
                            }
                        </td>
                        <td>
                            ${viaggio.data_partenza ?
                                `<span class="text-warning fw-bold">${formatDateForDisplay(viaggio.data_partenza)}</span>` :
                                `<span class="text-muted">N/A</span>`
                            }
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="/operativo/sof/viaggio/${viaggio.id}" class="btn btn-sm btn-outline-primary" title="Gestisci Viaggio">
                                    <i class="fas fa-cog"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-warning" title="Modifica Viaggio"
                                        onclick="modificaViaggio(${viaggio.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" title="Elimina Viaggio"
                                        onclick="eliminaViaggio(${viaggio.id}, '${viaggio.viaggio || ''}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        tbody.innerHTML = html;

        // Rimuovi evidenziazione dopo 3 secondi
        if (viaggioModificato) {
            setTimeout(() => {
                const rigaModificata = document.querySelector(`tr[data-viaggio-id="${viaggioModificato}"]`);
                if (rigaModificata) {
                    rigaModificata.style.backgroundColor = '';
                }
            }, 3000);
        }

        console.log('✅ Tabella aggiornata manualmente');
        return true;

    } catch (error) {
        console.error('❌ Errore aggiornamento manuale tabella:', error);
        return false;
    }
}

/**
 * Evidenzia la riga modificata
 */
function evidenziaRigaModificata(viaggioId) {
    try {
        const riga = document.querySelector(`tr[data-viaggio-id="${viaggioId}"]`);
        if (riga) {
            riga.style.backgroundColor = '#d4edda';
            riga.style.transition = 'background-color 0.3s ease';

            setTimeout(() => {
                riga.style.backgroundColor = '';
            }, 3000);

            console.log(`✨ Riga viaggio ${viaggioId} evidenziata`);
        }
    } catch (error) {
        console.error('❌ Errore evidenziazione riga:', error);
    }
}

/**
 * Aggiorna manualmente tutta la tabella
 */
async function aggiornaTabella() {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;

    try {
        // Mostra spinner
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Aggiornando...';
        btn.disabled = true;

        console.log('🔄 Aggiornamento manuale tabella richiesto');

        // Ricarica i dati
        const response = await fetch(`/api/sof/da-realizzare/search?_t=${Date.now()}`, {
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });

        const data = await response.json();

        if (data.success && typeof updateTableContent === 'function') {
            updateTableContent(data.viaggi);
            console.log('✅ Tabella aggiornata manualmente');

            // Mostra messaggio di successo
            if (typeof mostraSuccesso === 'function') {
                mostraSuccesso('Tabella aggiornata con successo!');
            }
        } else {
            throw new Error('Aggiornamento fallito');
        }
    } catch (error) {
        console.error('❌ Errore aggiornamento manuale:', error);
        if (typeof mostraErrore === 'function') {
            mostraErrore('Errore durante l\'aggiornamento della tabella');
        }
    } finally {
        // Ripristina pulsante
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    }
}

/**
 * Formatta una data per la visualizzazione
 */
function formatDateForDisplay(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'N/A';

        return date.toLocaleDateString('it-IT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    } catch (error) {
        console.error('Errore formattazione data:', error);
        return 'N/A';
    }
}

// Rendi le funzioni accessibili globalmente
window.popolaSelectPorti = popolaSelectPorti;
window.caricaPortiAtlas = caricaPortiAtlas;
window.aggiornaRigaViaggio = aggiornaRigaViaggio;
window.aggiornaTabella = aggiornaTabella;
window.aggiornaTabellaDopModifica = aggiornaTabellaDopModifica;
window.formatDateForDisplay = formatDateForDisplay;

console.log('🌍 Funzioni rese globali:', {
    popolaSelectPorti: typeof window.popolaSelectPorti,
    caricaPortiAtlas: typeof window.caricaPortiAtlas,
    aggiornaRigaViaggio: typeof window.aggiornaRigaViaggio,
    aggiornaTabella: typeof window.aggiornaTabella,
    aggiornaTabellaDopModifica: typeof window.aggiornaTabellaDopModifica,
    formatDateForDisplay: typeof window.formatDateForDisplay
});
