/* SNIP - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --maritime-blue: #1e3a8a;
    --maritime-light: #3b82f6;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-block {
    width: 100%;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* Forms */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--maritime-blue) 0%, var(--maritime-light) 100%);
}

.login-card {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.login-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
}

.login-header h2 {
    color: var(--maritime-blue);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #6c757d;
    margin-bottom: 0;
}

/* Dashboard Stats Cards */
.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

/* Navigation */
.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.15s ease-in-out;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* Pagination */
.pagination .page-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Maritime Theme */
.maritime-theme {
    --bs-primary: var(--maritime-blue);
    --bs-primary-rgb: 30, 58, 138;
}

.maritime-theme .navbar {
    background: linear-gradient(90deg, var(--maritime-blue) 0%, var(--maritime-light) 100%) !important;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background-color: var(--success-color);
}

.status-indicator.inactive {
    background-color: var(--danger-color);
}

.status-indicator.pending {
    background-color: var(--warning-color);
}

/* Utility Classes */
.text-maritime {
    color: var(--maritime-blue) !important;
}

.bg-maritime {
    background-color: var(--maritime-blue) !important;
}

.border-maritime {
    border-color: var(--maritime-blue) !important;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
