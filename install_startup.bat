@echo off
REM Script di installazione per l'avvio automatico del Sistema SNIP
REM Deve essere eseguito come Amministratore

echo ========================================
echo   Installazione Avvio Automatico SNIP
echo   Michele Autuori Srl
echo ========================================
echo.

REM Verifica privilegi di amministratore
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERRORE: Questo script deve essere eseguito come Amministratore
    echo.
    echo Fai clic destro su questo file e seleziona "Esegui come amministratore"
    pause
    exit /b 1
)

echo Privilegi di amministratore confermati.
echo.

REM Cambia directory al percorso del progetto
cd /d "%~dp0"

REM Verifica che i file necessari esistano
if not exist "start_snip_auth.ps1" (
    echo ERRORE: File start_snip_auth.ps1 non trovato
    echo Assicurati di essere nella directory corretta del progetto
    pause
    exit /b 1
)

echo File di avvio trovato: start_snip_auth.ps1
echo.

REM Configura l'esecuzione di PowerShell per questo utente
echo Configurazione policy di esecuzione PowerShell...
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"

if %errorLevel% neq 0 (
    echo ATTENZIONE: Impossibile configurare la policy di esecuzione PowerShell
    echo Potrebbe essere necessario configurarla manualmente
    echo.
)

REM Installa il task schedulato
echo Installazione task di avvio automatico...
powershell -ExecutionPolicy Bypass -File "start_snip_auth.ps1" -InstallStartup

if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo   INSTALLAZIONE COMPLETATA CON SUCCESSO
    echo ========================================
    echo.
    echo Il Sistema di Autenticazione SNIP e' stato configurato per:
    echo.
    echo 1. Avviarsi automaticamente all'avvio di Windows
    echo 2. Inviare un codice di <NAME_EMAIL>
    echo 3. Aprire automaticamente il browser per l'autenticazione
    echo 4. Avviare il servizio FastAPI solo dopo l'autenticazione
    echo.
    echo IMPORTANTE:
    echo - Al prossimo riavvio, il sistema si avviera' automaticamente
    echo - Controlla la tua email per il codice di sicurezza
    echo - Il servizio sara' disponibile su http://localhost:8000 dopo l'autenticazione
    echo.
    echo Per disinstallare l'avvio automatico, esegui: uninstall_startup.bat
    echo.
) else (
    echo.
    echo ========================================
    echo   ERRORE DURANTE L'INSTALLAZIONE
    echo ========================================
    echo.
    echo L'installazione del task di avvio automatico e' fallita.
    echo.
    echo Possibili cause:
    echo 1. Privilegi insufficienti
    echo 2. Policy di sicurezza di Windows
    echo 3. Antivirus che blocca la creazione di task
    echo.
    echo Prova a:
    echo 1. Eseguire questo script come Amministratore
    echo 2. Disabilitare temporaneamente l'antivirus
    echo 3. Configurare manualmente il task in "Utilita' di pianificazione"
    echo.
)

echo.
echo Vuoi testare il sistema ora? (s/n)
set /p test_now=

if /i "%test_now%"=="s" (
    echo.
    echo Avvio test del sistema...
    echo NOTA: Questo test NON configurera' l'avvio automatico
    echo.
    start_snip_auth.bat
)

pause
