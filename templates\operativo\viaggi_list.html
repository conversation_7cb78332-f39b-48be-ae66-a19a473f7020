{% extends "base.html" %}

{% block title %}Viaggi - SNIP{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-route"></i> Gestione Viaggi</h1>
            <a href="/viaggi/nuovo" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nuovo Viaggio
            </a>
        </div>
    </div>
</div>

<!-- Filtri e Ricerca -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">Ricerca</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="Cerca per viaggio, porto...">
                    </div>
                    <div class="col-md-3">
                        <label for="stato" class="form-label">Stato</label>
                        <select class="form-select" id="stato" name="stato">
                            <option value="">Tutti</option>
                            <option value="PROGRAMMATO">Programmato</option>
                            <option value="IN_CORSO">In Corso</option>
                            <option value="COMPLETATO">Completato</option>
                            <option value="ANNULLATO">Annullato</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search"></i> Cerca
                        </button>
                        <a href="/viaggi" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Lista Viaggi -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>Elenco Viaggi</h5>
            </div>
            <div class="card-body">
                {% if viaggi %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Viaggio</th>
                                <th>Nave</th>
                                <th>Armatore</th>
                                <th>Rotta</th>
                                <th>Data Partenza</th>
                                <th>Data Arrivo</th>
                                <th>Stato</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for viaggio in viaggi %}
                            <tr>
                                <td>
                                    <strong>{{ viaggio.viaggio }}</strong>
                                </td>
                                <td>
                                    {% if viaggio.nave %}
                                        {{ viaggio.nave.Nave }}
                                        <br><small class="text-muted">IMO: {{ viaggio.nave.IMO }}</small>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if viaggio.armatore %}
                                        {{ viaggio.armatore.nome }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <i class="fas fa-map-marker-alt text-success"></i> {{ viaggio.porto_partenza }}
                                    <br>
                                    <i class="fas fa-map-marker-alt text-danger"></i> {{ viaggio.porto_arrivo }}
                                </td>
                                <td>
                                    {% if viaggio.data_partenza %}
                                        {{ viaggio.data_partenza.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if viaggio.data_arrivo %}
                                        {{ viaggio.data_arrivo.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% set stato_class = {
                                        'PROGRAMMATO': 'secondary',
                                        'IN_CORSO': 'primary',
                                        'COMPLETATO': 'success',
                                        'ANNULLATO': 'danger',
                                        'SOSPESO': 'warning'
                                    } %}
                                    <span class="badge bg-{{ stato_class.get(viaggio.stato, 'secondary') }}">
                                        {{ viaggio.stato }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="/viaggi/{{ viaggio.id }}" class="btn btn-outline-primary" title="Visualizza">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/viaggi/{{ viaggio.id }}/edit" class="btn btn-outline-warning" title="Modifica">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/sof/genera/{{ viaggio.id }}" class="btn btn-outline-success" title="Genera SOF">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Paginazione -->
                {% if total_pages > 1 %}
                <nav aria-label="Paginazione viaggi">
                    <ul class="pagination justify-content-center">
                        {% if page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page - 1 }}{% if search %}&search={{ search }}{% endif %}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for p in range(1, total_pages + 1) %}
                            {% if p == page %}
                            <li class="page-item active">
                                <span class="page-link">{{ p }}</span>
                            </li>
                            {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ p }}{% if search %}&search={{ search }}{% endif %}">{{ p }}</a>
                            </li>
                            {% elif p == 4 or p == total_pages - 3 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page < total_pages %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page + 1 }}{% if search %}&search={{ search }}{% endif %}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-ship fa-3x mb-3"></i>
                    <h5>Nessun viaggio trovato</h5>
                    <p>Non ci sono viaggi che corrispondono ai criteri di ricerca.</p>
                    <a href="/viaggi/nuovo" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Crea il primo viaggio
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
