<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Contabilità - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- CSS Card Dashboard Professionali -->
    <link rel="stylesheet" href="/static/css/dashboard-cards.css">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-content {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Navbar principale -->
    {% include 'components/navbar.html' %}

    <div class="container main-content">
        <h1>Dashboard Reparto Contabilità</h1>
        <!-- Stat Cards Row -->
        <div class="row mt-4 justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card stat-card success" onclick="window.location.href='/contabilita/agemar'">
                    <div class="card-body text-center">
                        <i class="fas fa-ship stat-icon"></i>
                        <div class="stat-number counter" data-target="0" id="agemar-count">0</div>
                        <div class="stat-label">Calcoli AGEMAR</div>
                        <small class="text-muted d-block mt-1">Gestisci calcoli AGEMAR per contabilità</small>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Animazione counter per dashboard contabilità
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🎨 Inizializzazione dashboard contabilità moderna...');

        // Aggiungi effetti alle card
        initializeCardEffects();

        // Carica il conteggio reale dei calcoli AGEMAR
        loadAgemar Count();
    });

    // Funzione per caricare il conteggio AGEMAR
    async function loadAgemar Count() {
        try {
            const response = await fetch('/api/calcolo-agemar');
            const data = await response.json();

            if (data.success) {
                const count = data.total || 0;
                const agemar CountElement = document.getElementById('agemar-count');

                if (agemar CountElement) {
                    agemar CountElement.setAttribute('data-target', count);
                    animateCounterModern(agemar CountElement, count);
                }
            }
        } catch (error) {
            console.error('Errore nel caricamento conteggio AGEMAR:', error);
            // Fallback: anima con 0
            const agemar CountElement = document.getElementById('agemar-count');
            if (agemar CountElement) {
                animateCounterModern(agemar CountElement, 0);
            }
        }
    }

    // Inizializza effetti moderni per le card
    function initializeCardEffects() {
        const cards = document.querySelectorAll('.stat-card');

        cards.forEach((card, index) => {
            // Animazione di entrata scaglionata
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);

            // Effetto hover avanzato
            card.addEventListener('mouseenter', function() {
                this.classList.add('glow');
                const icon = this.querySelector('.stat-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                }
            });

            card.addEventListener('mouseleave', function() {
                this.classList.remove('glow');
                const icon = this.querySelector('.stat-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });
    }

    // Animazione counter moderna con effetti
    function animateCounterModern(element, target) {
        const duration = 2000;
        const start = 0;
        const startTime = performance.now();
        const isDecimal = target % 1 !== 0;

        element.classList.add('animate');

        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentValue = start + (target - start) * easeOutCubic;

            if (isDecimal) {
                element.textContent = currentValue.toFixed(1);
            } else {
                element.textContent = Math.floor(currentValue);
            }

            if (progress >= 0.9 && progress < 1) {
                element.style.transform = 'scale(1.1)';
            } else if (progress === 1) {
                element.style.transform = 'scale(1)';
                element.classList.remove('animate');

                element.style.textShadow = '0 0 20px rgba(255, 255, 255, 0.8)';
                setTimeout(() => {
                    element.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
                }, 500);
            }

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            } else {
                if (isDecimal) {
                    element.textContent = target.toFixed(1);
                } else {
                    element.textContent = target;
                }
            }
        }

        setTimeout(() => {
            requestAnimationFrame(updateCounter);
        }, 300);
    }
    </script>
</body>
</html> 