#!/usr/bin/env python3
"""
Server di autenticazione Flask per il sistema SNIP
Gestisce l'autenticazione prima di avviare il servizio FastAPI principale
"""

import os
import sys
import subprocess
import threading
import time
import logging
from flask import Flask, render_template, request, jsonify, redirect, url_for
from auth_system import auth_system

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auth_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Inizializza Flask
app = Flask(__name__, template_folder='templates')
app.secret_key = 'snip-auth-secret-key-2025'

# Variabili globali
fastapi_process = None
authenticated = False

@app.route('/')
def index():
    """Pagina principale di autenticazione"""
    global authenticated
    
    if authenticated:
        return redirect('/dashboard')
    
    return render_template('auth_login.html')

@app.route('/auth/request-code', methods=['POST'])
def request_code():
    """Endpoint per richiedere un nuovo codice di sicurezza"""
    try:
        success = auth_system.request_new_code()
        
        if success:
            logger.info("Nuovo codice di sicurezza richiesto e inviato")
            return jsonify({
                "success": True,
                "message": "Codice inviato via email!"
            })
        else:
            logger.error("Errore nell'invio del codice di sicurezza")
            return jsonify({
                "success": False,
                "message": "Errore nell'invio del codice. Verifica la configurazione email."
            }), 500
            
    except Exception as e:
        logger.error(f"Errore richiesta codice: {e}")
        return jsonify({
            "success": False,
            "message": "Errore interno del server"
        }), 500

@app.route('/auth/verify', methods=['POST'])
def verify_code():
    """Endpoint per verificare il codice inserito"""
    global authenticated
    
    try:
        data = request.get_json()
        code = data.get('code', '').strip()
        
        if not code:
            return jsonify({
                "success": False,
                "message": "Codice richiesto"
            }), 400
        
        result = auth_system.verify_code(code)
        
        if result['success']:
            authenticated = True
            logger.info("Autenticazione riuscita - Avvio servizio FastAPI")
            
            # Avvia il servizio FastAPI in background
            threading.Thread(target=start_fastapi_service, daemon=True).start()
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Errore verifica codice: {e}")
        return jsonify({
            "success": False,
            "message": "Errore interno del server"
        }), 500

@app.route('/auth/status')
def get_status():
    """Endpoint per ottenere lo stato dell'autenticazione"""
    try:
        status = auth_system.get_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Errore stato autenticazione: {e}")
        return jsonify({
            "error": "Errore interno del server"
        }), 500

@app.route('/dashboard')
def dashboard():
    """Pagina di dashboard dopo l'autenticazione"""
    global authenticated, fastapi_process
    
    if not authenticated:
        return redirect('/')
    
    # Verifica se FastAPI è in esecuzione
    fastapi_running = fastapi_process is not None and fastapi_process.poll() is None
    
    return f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🚀 Sistema SNIP - Avviato</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {{
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                min-height: 100vh;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }}
            .success-container {{
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }}
            .success-card {{
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                max-width: 500px;
                width: 100%;
                padding: 40px;
                text-align: center;
            }}
            .success-icon {{
                font-size: 4rem;
                color: #28a745;
                margin-bottom: 20px;
                animation: bounce 2s infinite;
            }}
            @keyframes bounce {{
                0%, 20%, 50%, 80%, 100% {{ transform: translateY(0); }}
                40% {{ transform: translateY(-10px); }}
                60% {{ transform: translateY(-5px); }}
            }}
            .btn-access {{
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                border: none;
                border-radius: 15px;
                padding: 15px 30px;
                font-size: 1.1rem;
                font-weight: 600;
                color: white;
                text-decoration: none;
                display: inline-block;
                margin: 10px;
                transition: all 0.3s ease;
            }}
            .btn-access:hover {{
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 123, 255, 0.3);
                color: white;
                text-decoration: none;
            }}
            .status-indicator {{
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 8px;
            }}
            .status-running {{ background-color: #28a745; }}
            .status-stopped {{ background-color: #dc3545; }}
        </style>
    </head>
    <body>
        <div class="success-container">
            <div class="success-card">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                
                <h1 class="text-success mb-3">🎉 Autenticazione Riuscita!</h1>
                <p class="text-muted mb-4">Il sistema SNIP è stato avviato con successo.</p>
                
                <div class="mb-4">
                    <h5>Stato Servizi:</h5>
                    <p>
                        <span class="status-indicator {'status-running' if fastapi_running else 'status-stopped'}"></span>
                        FastAPI Service: {'🟢 In Esecuzione' if fastapi_running else '🔴 Non Attivo'}
                    </p>
                </div>
                
                {'<a href="http://localhost:8000" class="btn-access" target="_blank"><i class="fas fa-external-link-alt"></i> Accedi al Sistema SNIP</a>' if fastapi_running else '<p class="text-warning">⏳ Avvio del servizio in corso...</p>'}
                
                <div class="mt-4">
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-sync-alt"></i> Aggiorna Stato
                    </button>
                </div>
                
                <div class="mt-4 pt-3 border-top">
                    <small class="text-muted">
                        <strong>Michele Autuori Srl</strong><br>
                        Sistema Navale Integrato Portuale<br>
                        Autenticato alle {time.strftime('%H:%M:%S')}
                    </small>
                </div>
            </div>
        </div>
        
        <script>
            // Auto-refresh ogni 5 secondi se FastAPI non è ancora attivo
            {'setTimeout(() => location.reload(), 5000);' if not fastapi_running else ''}
        </script>
    </body>
    </html>
    """

def start_fastapi_service():
    """Avvia il servizio FastAPI principale"""
    global fastapi_process
    
    try:
        logger.info("Avvio del servizio FastAPI...")
        
        # Comando per avviare FastAPI
        cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
        
        # Avvia il processo FastAPI
        fastapi_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        logger.info(f"Servizio FastAPI avviato con PID: {fastapi_process.pid}")
        
        # Monitora il processo
        while fastapi_process.poll() is None:
            time.sleep(1)
        
        # Se il processo termina
        stdout, stderr = fastapi_process.communicate()
        logger.error(f"Servizio FastAPI terminato. STDOUT: {stdout.decode()}, STDERR: {stderr.decode()}")
        
    except Exception as e:
        logger.error(f"Errore avvio servizio FastAPI: {e}")

@app.route('/shutdown', methods=['POST'])
def shutdown():
    """Endpoint per spegnere il sistema"""
    global fastapi_process, authenticated
    
    try:
        # Termina il processo FastAPI se attivo
        if fastapi_process and fastapi_process.poll() is None:
            fastapi_process.terminate()
            logger.info("Servizio FastAPI terminato")
        
        authenticated = False
        
        # Spegni il server Flask
        func = request.environ.get('werkzeug.server.shutdown')
        if func is None:
            raise RuntimeError('Not running with the Werkzeug Server')
        func()
        
        return jsonify({"message": "Sistema spento con successo"})
        
    except Exception as e:
        logger.error(f"Errore spegnimento sistema: {e}")
        return jsonify({"error": str(e)}), 500

def main():
    """Funzione principale"""
    logger.info("🔐 Avvio Sistema di Autenticazione SNIP")
    logger.info("=" * 50)
    
    # Invia automaticamente il primo codice all'avvio
    logger.info("📧 Invio automatico del codice di sicurezza...")
    if auth_system.request_new_code():
        logger.info("✅ Codice inviato con successo!")
    else:
        logger.error("❌ Errore nell'invio del codice iniziale")
    
    # Avvia il server Flask
    logger.info("🌐 Avvio server di autenticazione su http://localhost:5000")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False
        )
    except KeyboardInterrupt:
        logger.info("Sistema interrotto dall'utente")
    except Exception as e:
        logger.error(f"Errore server di autenticazione: {e}")
    finally:
        # Cleanup
        if fastapi_process and fastapi_process.poll() is None:
            fastapi_process.terminate()
            logger.info("Servizio FastAPI terminato durante cleanup")

if __name__ == "__main__":
    main()
