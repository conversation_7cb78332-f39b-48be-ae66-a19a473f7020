#!/usr/bin/env python3
"""
Sistema di autenticazione per l'avvio del servizio FastAPI
Genera e invia codici di sicurezza di 6 cifre via email
"""

import random
import string
import smtplib
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auth_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AuthenticationSystem:
    """Sistema di autenticazione con codici di sicurezza via email"""
    
    def __init__(self, config_file: str = "auth_config.json"):
        self.config_file = config_file
        self.current_code = None
        self.code_expiry = None
        self.max_attempts = 3
        self.current_attempts = 0
        self.load_config()
    
    def load_config(self):
        """Carica la configurazione dal file JSON o dal database SNIP"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # Prova a caricare configurazioni dal database SNIP
                email_config = self.load_email_config_from_db()

                if email_config:
                    logger.info("Configurazioni email caricate dal database SNIP")
                    self.config = {
                        "email": email_config,
                        "security": {
                            "code_length": 6,
                            "code_expiry_minutes": 10,
                            "max_attempts": 3
                        }
                    }
                else:
                    # Configurazione predefinita
                    logger.warning("Usando configurazione email predefinita")
                    self.config = {
                        "email": {
                            "smtp_host": "smtp.gmail.com",
                            "smtp_port": 587,
                            "smtp_username": "<EMAIL>",
                            "smtp_password": "sxfv xkjf afur vuhr",  # Password fornita dall'utente
                            "sender_email": "<EMAIL>",
                            "sender_name": "Sistema SNIP",
                            "target_email": "<EMAIL>",
                            "smtp_ssl": True
                        },
                        "security": {
                            "code_length": 6,
                            "code_expiry_minutes": 10,
                            "max_attempts": 3
                        }
                    }

                self.save_config()

            logger.info("Configurazione caricata con successo")

        except Exception as e:
            logger.error(f"Errore caricamento configurazione: {e}")
            raise

    def load_email_config_from_db(self):
        """Carica configurazioni email dal database SNIP"""
        try:
            # Importa le funzioni dal main.py
            import sys
            sys.path.append('.')
            from main import get_email_config
            from database import SessionLocal

            with SessionLocal() as db:
                db_config = get_email_config(db)

                # Converte la configurazione del database nel formato richiesto
                return {
                    "smtp_host": db_config.get("smtp_host", "smtp.gmail.com"),
                    "smtp_port": db_config.get("smtp_port", 587),
                    "smtp_username": db_config.get("smtp_username", ""),
                    "smtp_password": db_config.get("smtp_password", ""),
                    "sender_email": db_config.get("sender_email", ""),
                    "sender_name": db_config.get("sender_name", "Sistema SNIP"),
                    "target_email": "<EMAIL>",  # Email fissa per l'autenticazione
                    "smtp_ssl": db_config.get("smtp_ssl", True)
                }

        except Exception as e:
            logger.warning(f"Impossibile caricare configurazioni dal database: {e}")
            return None
    
    def save_config(self):
        """Salva la configurazione nel file JSON"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            logger.info("Configurazione salvata con successo")
        except Exception as e:
            logger.error(f"Errore salvataggio configurazione: {e}")
    
    def generate_security_code(self) -> str:
        """Genera un codice di sicurezza di 6 cifre"""
        code_length = self.config["security"]["code_length"]
        code = ''.join(random.choices(string.digits, k=code_length))
        
        # Imposta scadenza
        expiry_minutes = self.config["security"]["code_expiry_minutes"]
        self.code_expiry = datetime.now() + timedelta(minutes=expiry_minutes)
        
        self.current_code = code
        self.current_attempts = 0
        
        logger.info(f"Codice di sicurezza generato (scade alle {self.code_expiry.strftime('%H:%M:%S')})")
        return code
    
    def send_security_code(self, code: str) -> bool:
        """Invia il codice di sicurezza via email"""
        try:
            email_config = self.config["email"]
            
            # Verifica configurazione email
            if not email_config.get("smtp_password"):
                logger.error("Password SMTP non configurata")
                return False
            
            if not email_config.get("target_email"):
                logger.error("Email destinatario non configurata")
                return False
            
            # Crea messaggio email
            msg = MIMEMultipart()
            msg['From'] = f"{email_config['sender_name']} <{email_config['sender_email']}>"
            msg['To'] = email_config['target_email']
            msg['Subject'] = "🔐 Codice di Accesso Sistema SNIP"
            
            # Corpo del messaggio
            body = f"""
Gentile Utente,

È stato richiesto l'accesso al Sistema SNIP (Sistema Navale Integrato Portuale).

🔑 CODICE DI ACCESSO: {code}

⏰ VALIDITÀ: {self.config['security']['code_expiry_minutes']} minuti
🕒 SCADENZA: {self.code_expiry.strftime('%H:%M:%S')}

⚠️ IMPORTANTE:
- Questo codice è valido solo per {self.config['security']['code_expiry_minutes']} minuti
- Hai a disposizione {self.config['security']['max_attempts']} tentativi
- Non condividere questo codice con nessuno

Se non hai richiesto questo accesso, ignora questa email.

---
Sistema di Sicurezza SNIP
Michele Autuori Srl - shipping and forwarding agency
Generato il {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # Connessione SMTP
            server = smtplib.SMTP(email_config['smtp_host'], email_config['smtp_port'])
            
            if email_config.get('smtp_ssl', True):
                server.starttls()
            
            server.login(email_config['smtp_username'], email_config['smtp_password'])
            
            # Invia email
            text = msg.as_string()
            server.sendmail(email_config['sender_email'], email_config['target_email'], text)
            server.quit()
            
            logger.info(f"Codice di sicurezza inviato a {email_config['target_email']}")
            return True
            
        except Exception as e:
            logger.error(f"Errore invio email: {e}")
            return False
    
    def verify_code(self, input_code: str) -> Dict[str, Any]:
        """Verifica il codice inserito dall'utente"""
        result = {
            "success": False,
            "message": "",
            "attempts_remaining": 0,
            "code_expired": False
        }
        
        # Verifica se c'è un codice attivo
        if not self.current_code:
            result["message"] = "Nessun codice attivo. Richiedi un nuovo codice."
            return result
        
        # Verifica scadenza
        if datetime.now() > self.code_expiry:
            result["code_expired"] = True
            result["message"] = "Il codice è scaduto. Richiedi un nuovo codice."
            self.current_code = None
            return result
        
        # Incrementa tentativi
        self.current_attempts += 1
        attempts_remaining = self.max_attempts - self.current_attempts
        result["attempts_remaining"] = attempts_remaining
        
        # Verifica codice
        if input_code == self.current_code:
            result["success"] = True
            result["message"] = "Codice corretto! Accesso autorizzato."
            self.current_code = None  # Invalida il codice dopo l'uso
            logger.info("Autenticazione riuscita")
            return result
        
        # Codice errato
        if attempts_remaining > 0:
            result["message"] = f"Codice errato. Tentativi rimanenti: {attempts_remaining}"
        else:
            result["message"] = "Troppi tentativi falliti. Richiedi un nuovo codice."
            self.current_code = None  # Invalida il codice
        
        logger.warning(f"Tentativo di autenticazione fallito. Tentativi rimanenti: {attempts_remaining}")
        return result
    
    def request_new_code(self) -> bool:
        """Richiede un nuovo codice di sicurezza"""
        try:
            code = self.generate_security_code()
            success = self.send_security_code(code)
            
            if success:
                logger.info("Nuovo codice di sicurezza richiesto e inviato")
            else:
                logger.error("Errore nell'invio del nuovo codice")
            
            return success
            
        except Exception as e:
            logger.error(f"Errore richiesta nuovo codice: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Verifica se l'utente è attualmente autenticato"""
        return self.current_code is None and self.current_attempts == 0
    
    def get_status(self) -> Dict[str, Any]:
        """Ottiene lo stato corrente del sistema di autenticazione"""
        status = {
            "has_active_code": self.current_code is not None,
            "code_expired": False,
            "attempts_used": self.current_attempts,
            "max_attempts": self.max_attempts,
            "attempts_remaining": self.max_attempts - self.current_attempts,
            "expiry_time": None
        }
        
        if self.current_code and self.code_expiry:
            status["code_expired"] = datetime.now() > self.code_expiry
            status["expiry_time"] = self.code_expiry.strftime('%H:%M:%S')
        
        return status

# Istanza globale del sistema di autenticazione
auth_system = AuthenticationSystem()

if __name__ == "__main__":
    # Test del sistema
    print("🔐 Test Sistema di Autenticazione SNIP")
    print("=" * 50)

    # Richiedi nuovo codice
    print("📧 Invio codice di sicurezza...")
    if auth_system.request_new_code():
        print("✅ Codice inviato con successo!")

        # Simula inserimento codice
        while True:
            user_input = input("\n🔑 Inserisci il codice ricevuto via email (o 'quit' per uscire): ")

            if user_input.lower() == 'quit':
                break

            result = auth_system.verify_code(user_input)
            print(f"📋 {result['message']}")

            if result['success']:
                print("🎉 Autenticazione completata!")
                break
            elif result['code_expired']:
                break
            elif result['attempts_remaining'] == 0:
                break
    else:
        print("❌ Errore nell'invio del codice")
