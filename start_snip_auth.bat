@echo off
REM Script di avvio automatico per il Sistema di Autenticazione SNIP
REM Questo script viene eseguito all'avvio di Windows

echo ========================================
echo    Sistema di Autenticazione SNIP
echo    Michele Autuori Srl
echo ========================================
echo.

REM Cambia directory al percorso del progetto
cd /d "%~dp0"

REM Verifica che Python sia installato
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRORE: Python non trovato nel PATH
    echo Installa Python e assicurati che sia nel PATH di sistema
    pause
    exit /b 1
)

REM Verifica che i file necessari esistano
if not exist "auth_server.py" (
    echo ERRORE: File auth_server.py non trovato
    echo Assicurati di essere nella directory corretta del progetto
    pause
    exit /b 1
)

if not exist "auth_system.py" (
    echo ERRORE: File auth_system.py non trovato
    echo Assicurati di essere nella directory corretta del progetto
    pause
    exit /b 1
)

REM Installa le dipendenze se necessario
echo Verifica dipendenze Python...
pip install flask requests >nul 2>&1

REM Crea log directory se non esiste
if not exist "logs" mkdir logs

REM Avvia il sistema di autenticazione
echo.
echo Avvio Sistema di Autenticazione SNIP...
echo.
echo IMPORTANTE:
echo - Un codice di sicurezza sara' <NAME_EMAIL>
echo - Apri il browser su http://localhost:8002 per inserire il codice
echo - Solo dopo l'autenticazione il servizio FastAPI si avviera'
echo.

REM Avvia il server di autenticazione
python auth_server.py

REM Se arriviamo qui, il server si è chiuso
echo.
echo Sistema di autenticazione terminato.
pause
