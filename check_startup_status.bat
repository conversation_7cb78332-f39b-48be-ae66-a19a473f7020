@echo off
REM Script per verificare lo stato dell'avvio automatico

echo ========================================
echo   Verifica Avvio Automatico SNIP
echo ========================================
echo.

REM Verifica se il task schedulato esiste
echo Verifica task schedulato...
schtasks /query /tn "SNIP_Authentication_System" >nul 2>&1

if %errorLevel% equ 0 (
    echo ✅ Task di avvio automatico TROVATO
    echo.
    echo Dettagli del task:
    schtasks /query /tn "SNIP_Authentication_System" /fo LIST /v | findstr /C:"Task Name" /C:"Status" /C:"Next Run Time" /C:"Last Run Time"
    echo.
    echo Il sistema e' configurato per l'avvio automatico.
    echo.
    echo Opzioni disponibili:
    echo 1. Esegui test manuale (test_auth_startup.bat)
    echo 2. Disinstalla avvio automatico (uninstall_startup.bat)
    echo 3. Forza esecuzione immediata del task
    echo.
    set /p choice="Scegli un'opzione (1-3) o premi Enter per uscire: "
    
    if "%choice%"=="1" (
        echo.
        echo Avvio test manuale...
        call test_auth_startup.bat
    ) else if "%choice%"=="2" (
        echo.
        echo Avvio disinstallazione...
        call uninstall_startup.bat
    ) else if "%choice%"=="3" (
        echo.
        echo Esecuzione immediata del task...
        schtasks /run /tn "SNIP_Authentication_System"
        echo Task avviato. Controlla se si apre il browser.
    )
    
) else (
    echo ❌ Task di avvio automatico NON TROVATO
    echo.
    echo Il sistema NON e' configurato per l'avvio automatico.
    echo.
    echo Per configurare l'avvio automatico:
    echo 1. Esegui install_startup.bat come Amministratore
    echo.
    echo Per testare manualmente:
    echo 2. Esegui test_auth_startup.bat
    echo.
    set /p choice="Vuoi eseguire il test manuale ora? (s/n): "
    
    if /i "%choice%"=="s" (
        echo.
        echo Avvio test manuale...
        call test_auth_startup.bat
    )
)

echo.
pause
