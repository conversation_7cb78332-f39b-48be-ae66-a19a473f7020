@echo off
REM Script di disinstallazione per l'avvio automatico del Sistema SNIP

echo ========================================
echo  Disinstallazione Avvio Automatico SNIP
echo  Michele Autuori Srl
echo ========================================
echo.

REM Verifica privilegi di amministratore
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERRORE: Questo script deve essere eseguito come Amministratore
    echo.
    echo Fai clic destro su questo file e seleziona "Esegui come amministratore"
    pause
    exit /b 1
)

echo Privilegi di amministratore confermati.
echo.

REM Cambia directory al percorso del progetto
cd /d "%~dp0"

REM Verifica che il file PowerShell esista
if not exist "start_snip_auth.ps1" (
    echo ATTENZIONE: File start_snip_auth.ps1 non trovato
    echo Tentativo di rimozione manuale del task...
    echo.
    
    REM Tentativo di rimozione manuale
    schtasks /delete /tn "SNIP_Authentication_System" /f >nul 2>&1
    
    if %errorLevel% equ 0 (
        echo Task rimosso manualmente con successo.
    ) else (
        echo Nessun task trovato o errore nella rimozione.
    )
    
    echo.
    pause
    exit /b 0
)

echo Rimozione task di avvio automatico...
powershell -ExecutionPolicy Bypass -File "start_snip_auth.ps1" -UninstallStartup

if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo  DISINSTALLAZIONE COMPLETATA
    echo ========================================
    echo.
    echo Il Sistema di Autenticazione SNIP non si avviera' piu'
    echo automaticamente all'avvio di Windows.
    echo.
    echo Per riattivare l'avvio automatico, esegui: install_startup.bat
    echo.
    echo NOTA: I file del sistema non sono stati eliminati.
    echo Puoi ancora avviare manualmente il sistema con: start_snip_auth.bat
    echo.
) else (
    echo.
    echo ========================================
    echo   ERRORE DURANTE LA DISINSTALLAZIONE
    echo ========================================
    echo.
    echo La rimozione del task di avvio automatico e' fallita.
    echo.
    echo Puoi provare a rimuoverlo manualmente:
    echo 1. Apri "Utilita' di pianificazione" (taskschd.msc)
    echo 2. Cerca il task "SNIP_Authentication_System"
    echo 3. Eliminalo manualmente
    echo.
)

pause
