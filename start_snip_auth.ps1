# Script PowerShell per l'avvio automatico del Sistema di Autenticazione SNIP
# Michele Autuori <PERSON>l - Sistema Navale Integrato Portuale

param(
    [switch]$Silent,
    [switch]$AutoOpen
)

# Configurazione
$ProjectPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$LogFile = Join-Path $ProjectPath "logs\startup.log"
$AuthServerScript = Join-Path $ProjectPath "auth_server.py"
$AuthSystemScript = Join-Path $ProjectPath "auth_system.py"

# Funzione per scrivere log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    
    if (-not $Silent) {
        Write-Host $LogMessage
    }
    
    # Crea directory logs se non esiste
    $LogDir = Split-Path -Parent $LogFile
    if (-not (Test-Path $LogDir)) {
        New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
    }
    
    Add-Content -Path $LogFile -Value $LogMessage
}

# Funzione per verificare i prerequisiti
function Test-Prerequisites {
    Write-Log "Verifica prerequisiti sistema..."
    
    # Verifica Python
    try {
        $PythonVersion = python --version 2>&1
        Write-Log "Python trovato: $PythonVersion"
    }
    catch {
        Write-Log "ERRORE: Python non trovato nel PATH" "ERROR"
        return $false
    }
    
    # Verifica file necessari
    if (-not (Test-Path $AuthServerScript)) {
        Write-Log "ERRORE: File auth_server.py non trovato in $AuthServerScript" "ERROR"
        return $false
    }
    
    if (-not (Test-Path $AuthSystemScript)) {
        Write-Log "ERRORE: File auth_system.py non trovato in $AuthSystemScript" "ERROR"
        return $false
    }
    
    Write-Log "Tutti i prerequisiti soddisfatti"
    return $true
}

# Funzione per installare dipendenze
function Install-Dependencies {
    Write-Log "Installazione dipendenze Python..."
    
    try {
        $RequiredPackages = @("flask", "requests")
        
        foreach ($Package in $RequiredPackages) {
            Write-Log "Installazione $Package..."
            pip install $Package --quiet
        }
        
        Write-Log "Dipendenze installate con successo"
        return $true
    }
    catch {
        Write-Log "ERRORE: Installazione dipendenze fallita - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Funzione per aprire il browser
function Open-Browser {
    param([string]$Url = "http://localhost:8002")

    try {
        Start-Process $Url
        Write-Log "Browser aperto su $Url"
    }
    catch {
        Write-Log "Impossibile aprire il browser automaticamente" "WARN"
        Write-Log "Apri manualmente: $Url" "INFO"
    }
}

# Funzione per avviare il sistema di autenticazione
function Start-AuthSystem {
    Write-Log "Avvio Sistema di Autenticazione SNIP..."
    
    # Cambia directory al progetto
    Set-Location $ProjectPath
    
    try {
        # Avvia il server di autenticazione
        if ($AutoOpen) {
            # Avvia il server in background e apri il browser
            $Job = Start-Job -ScriptBlock {
                param($ScriptPath)
                Set-Location (Split-Path -Parent $ScriptPath)
                python $ScriptPath
            } -ArgumentList $AuthServerScript
            
            # Aspetta un momento per far partire il server
            Start-Sleep -Seconds 3
            Open-Browser
            
            # Aspetta che il job finisca
            Wait-Job $Job
            Receive-Job $Job
            Remove-Job $Job
        }
        else {
            # Avvia il server normalmente
            python $AuthServerScript
        }
    }
    catch {
        Write-Log "ERRORE: Avvio sistema fallito - $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    return $true
}

# Funzione per creare task schedulato di Windows
function Install-StartupTask {
    param([switch]$Force)
    
    $TaskName = "SNIP_Authentication_System"
    $TaskDescription = "Sistema di Autenticazione SNIP - Michele Autuori Srl"
    
    Write-Log "Installazione task di avvio automatico..."
    
    try {
        # Verifica se il task esiste già
        $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        
        if ($ExistingTask -and -not $Force) {
            Write-Log "Task di avvio già esistente. Usa -Force per sovrascrivere" "WARN"
            return $false
        }
        
        if ($ExistingTask) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            Write-Log "Task esistente rimosso"
        }
        
        # Crea azione del task
        $Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$($MyInvocation.MyCommand.Path)`" -Silent -AutoOpen"
        
        # Crea trigger (all'avvio del sistema)
        $Trigger = New-ScheduledTaskTrigger -AtStartup
        
        # Crea impostazioni del task
        $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # Crea principal (esegui come utente corrente)
        $Principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # Registra il task
        Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal -Description $TaskDescription
        
        Write-Log "Task di avvio automatico installato con successo"
        Write-Log "Il sistema si avvierà automaticamente al prossimo riavvio di Windows"
        
        return $true
    }
    catch {
        Write-Log "ERRORE: Installazione task fallita - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Funzione per rimuovere task schedulato
function Uninstall-StartupTask {
    $TaskName = "SNIP_Authentication_System"
    
    try {
        $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        
        if ($ExistingTask) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            Write-Log "Task di avvio automatico rimosso con successo"
        }
        else {
            Write-Log "Nessun task di avvio trovato"
        }
        
        return $true
    }
    catch {
        Write-Log "ERRORE: Rimozione task fallita - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Funzione principale
function Main {
    if (-not $Silent) {
        Write-Host "========================================" -ForegroundColor Cyan
        Write-Host "   Sistema di Autenticazione SNIP" -ForegroundColor Cyan
        Write-Host "   Michele Autuori Srl" -ForegroundColor Cyan
        Write-Host "========================================" -ForegroundColor Cyan
        Write-Host ""
    }
    
    Write-Log "Avvio Sistema di Autenticazione SNIP"
    
    # Verifica prerequisiti
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisiti non soddisfatti. Uscita." "ERROR"
        if (-not $Silent) {
            Read-Host "Premi Enter per continuare"
        }
        exit 1
    }
    
    # Installa dipendenze
    if (-not (Install-Dependencies)) {
        Write-Log "Installazione dipendenze fallita. Uscita." "ERROR"
        if (-not $Silent) {
            Read-Host "Premi Enter per continuare"
        }
        exit 1
    }
    
    # Avvia sistema
    Write-Log "IMPORTANTE:"
    Write-Log "- Un codice di sicurezza sarà <NAME_EMAIL>"
    Write-Log "- Apri il browser su http://localhost:8002 per inserire il codice"
    Write-Log "- Solo dopo l'autenticazione il servizio FastAPI si avvierà"
    
    if (Start-AuthSystem) {
        Write-Log "Sistema terminato normalmente"
    }
    else {
        Write-Log "Sistema terminato con errori" "ERROR"
        if (-not $Silent) {
            Read-Host "Premi Enter per continuare"
        }
        exit 1
    }
}

# Gestione parametri della riga di comando
if ($args -contains "-InstallStartup") {
    Install-StartupTask -Force
    exit 0
}

if ($args -contains "-UninstallStartup") {
    Uninstall-StartupTask
    exit 0
}

# Esegui funzione principale
Main
