{% extends "base.html" %}

{% block title %}Dashboard - SNIP{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
    </div>
</div>

<!-- Statistiche -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.viaggi_totali or 0 }}</h4>
                        <p class="mb-0">Viaggi Totali</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-route fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.viaggi_in_corso or 0 }}</h4>
                        <p class="mb-0">In Corso</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.navi_totali or 0 }}</h4>
                        <p class="mb-0">Navi</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-ship fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.sof_totali or 0 }}</h4>
                        <p class="mb-0">SOF Generati</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Azioni Rapide -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Azioni Rapide</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="/viaggi/nuovo" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-plus"></i> Nuovo Viaggio
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/sof/nuovo" class="btn btn-success btn-block w-100">
                            <i class="fas fa-file-plus"></i> Nuovo SOF
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/contabilita/agemar" class="btn btn-info btn-block w-100">
                            <i class="fas fa-calculator"></i> AGEMAR
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/reports" class="btn btn-warning btn-block w-100">
                            <i class="fas fa-chart-bar"></i> Report
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Viaggi Recenti -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Viaggi Recenti</h5>
            </div>
            <div class="card-body">
                {% if viaggi_recenti %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Viaggio</th>
                                <th>Nave</th>
                                <th>Rotta</th>
                                <th>Data</th>
                                <th>Stato</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for viaggio in viaggi_recenti %}
                            <tr>
                                <td>
                                    <a href="/viaggi/{{ viaggio.id }}">{{ viaggio.viaggio }}</a>
                                </td>
                                <td>{{ viaggio.nave.Nave if viaggio.nave else 'N/A' }}</td>
                                <td>{{ viaggio.porto_partenza }} → {{ viaggio.porto_arrivo }}</td>
                                <td>{{ viaggio.data_partenza.strftime('%d/%m/%Y') if viaggio.data_partenza else 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if viaggio.stato == 'COMPLETATO' else 'primary' if viaggio.stato == 'IN_CORSO' else 'secondary' }}">
                                        {{ viaggio.stato }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="/viaggi" class="btn btn-outline-primary btn-sm">
                        Vedi tutti i viaggi <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle"></i> Nessun viaggio recente
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bell"></i> Notifiche</h5>
            </div>
            <div class="card-body">
                {% if notifiche %}
                    {% for notifica in notifiche %}
                    <div class="alert alert-{{ notifica.tipo.lower() }} alert-sm">
                        <small>{{ notifica.messaggio }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-check-circle"></i> Nessuna notifica
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
