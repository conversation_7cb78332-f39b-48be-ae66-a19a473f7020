@echo off
REM Script di test per verificare l'avvio del sistema di autenticazione

echo ========================================
echo   TEST Sistema di Autenticazione SNIP
echo ========================================
echo.

REM Cambia directory al percorso del progetto
cd /d "%~dp0"

echo Verifica file necessari...
if not exist "auth_server.py" (
    echo ERRORE: File auth_server.py non trovato
    pause
    exit /b 1
)

if not exist "auth_system.py" (
    echo ERRORE: File auth_system.py non trovato
    pause
    exit /b 1
)

if not exist "templates\auth_login.html" (
    echo ERRORE: File templates\auth_login.html non trovato
    pause
    exit /b 1
)

echo ✅ Tutti i file necessari trovati
echo.

echo Verifica Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRORE: Python non trovato
    pause
    exit /b 1
)

echo ✅ Python trovato
echo.

echo Verifica Flask...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ERRORE: Flask non installato
    echo Installazione Flask...
    pip install flask
)

echo ✅ Flask disponibile
echo.

echo ========================================
echo   AVVIO SISTEMA DI AUTENTICAZIONE
echo ========================================
echo.
echo IMPORTANTE:
echo 1. Un codice sara' <NAME_EMAIL>
echo 2. Il browser si aprira' automaticamente su http://localhost:5000
echo 3. Inserisci il codice ricevuto via email
echo 4. Il servizio FastAPI si avviera' dopo l'autenticazione
echo.
echo Premi un tasto per continuare...
pause >nul

echo.
echo Avvio del server di autenticazione...
echo.

REM Avvia il server e apri il browser
start "" "http://localhost:5000"
python auth_server.py

echo.
echo Sistema terminato.
pause
